GEM
  remote: https://rubygems.org/
  specs:
    actioncable (8.0.2)
      actionpack (= 8.0.2)
      activesupport (= 8.0.2)
      nio4r (~> 2.0)
      websocket-driver (>= 0.6.1)
      zeitwerk (~> 2.6)
    actionmailbox (8.0.2)
      actionpack (= 8.0.2)
      activejob (= 8.0.2)
      activerecord (= 8.0.2)
      activestorage (= 8.0.2)
      activesupport (= 8.0.2)
      mail (>= 2.8.0)
    actionmailer (8.0.2)
      actionpack (= 8.0.2)
      actionview (= 8.0.2)
      activejob (= 8.0.2)
      activesupport (= 8.0.2)
      mail (>= 2.8.0)
      rails-dom-testing (~> 2.2)
    actionpack (8.0.2)
      actionview (= 8.0.2)
      activesupport (= 8.0.2)
      nokogiri (>= 1.8.5)
      rack (>= 2.2.4)
      rack-session (>= 1.0.1)
      rack-test (>= 0.6.3)
      rails-dom-testing (~> 2.2)
      rails-html-sanitizer (~> 1.6)
      useragent (~> 0.16)
    actiontext (8.0.2)
      actionpack (= 8.0.2)
      activerecord (= 8.0.2)
      activestorage (= 8.0.2)
      activesupport (= 8.0.2)
      globalid (>= 0.6.0)
      nokogiri (>= 1.8.5)
    actionview (8.0.2)
      activesupport (= 8.0.2)
      builder (~> 3.1)
      erubi (~> 1.11)
      rails-dom-testing (~> 2.2)
      rails-html-sanitizer (~> 1.6)
    activejob (8.0.2)
      activesupport (= 8.0.2)
      globalid (>= 0.3.6)
    activemodel (8.0.2)
      activesupport (= 8.0.2)
    activerecord (8.0.2)
      activemodel (= 8.0.2)
      activesupport (= 8.0.2)
      timeout (>= 0.4.0)
    activerecord-import (2.2.0)
      activerecord (>= 4.2)
    activestorage (8.0.2)
      actionpack (= 8.0.2)
      activejob (= 8.0.2)
      activerecord (= 8.0.2)
      activesupport (= 8.0.2)
      marcel (~> 1.0)
    activesupport (8.0.2)
      base64
      benchmark (>= 0.3)
      bigdecimal
      concurrent-ruby (~> 1.0, >= 1.3.1)
      connection_pool (>= 2.2.5)
      drb
      i18n (>= 1.6, < 2)
      logger (>= 1.4.2)
      minitest (>= 5.1)
      securerandom (>= 0.3)
      tzinfo (~> 2.0, >= 2.0.5)
      uri (>= 0.13.1)
    addressable (2.8.7)
      public_suffix (>= 2.0.2, < 7.0)
    annotate (2.6.5)
      activerecord (>= 2.3.0)
      rake (>= 0.8.7)
    argon2-kdf (0.3.1)
      fiddle
    ast (2.4.3)
    base64 (0.3.0)
    bcrypt (3.1.20)
    bcrypt_pbkdf (1.1.1)
    benchmark (0.4.1)
    bigdecimal (3.2.2)
    blind_index (2.7.0)
      activesupport (>= 7.1)
      argon2-kdf (>= 0.2)
    bootsnap (1.18.6)
      msgpack (~> 1.2)
    brakeman (7.0.2)
      racc
    builder (3.3.0)
    bullet (8.0.8)
      activesupport (>= 3.0.0)
      uniform_notifier (~> 1.11)
    chartkick (5.2.0)
    childprocess (5.1.0)
      logger (~> 1.5)
    chronic (0.10.2)
    concurrent-ruby (1.3.5)
    connection_pool (2.5.3)
    crack (1.0.0)
      bigdecimal
      rexml
    crass (1.0.6)
    creek (2.6.3)
      nokogiri (>= 1.10.0)
      rubyzip (>= 1.0.0)
    csv (3.3.5)
    database_cleaner-active_record (2.2.1)
      activerecord (>= 5.a)
      database_cleaner-core (~> 2.0.0)
    database_cleaner-core (2.0.1)
    date (3.4.1)
    debug (1.11.0)
      irb (~> 1.10)
      reline (>= 0.3.8)
    devise (4.9.4)
      bcrypt (~> 3.0)
      orm_adapter (~> 0.1)
      railties (>= 4.1.0)
      responders
      warden (~> 1.2.3)
    diff-lcs (1.6.2)
    docile (1.4.1)
    domain_name (0.6.20240107)
    dotenv (3.1.8)
    drb (2.2.3)
    ed25519 (1.4.0)
    erb (5.0.1)
    erubi (1.13.1)
    et-orbi (1.2.11)
      tzinfo
    factory_bot (6.5.4)
      activesupport (>= 6.1.0)
    factory_bot_rails (6.5.0)
      factory_bot (~> 6.5)
      railties (>= 6.1.0)
    faker (3.5.1)
      i18n (>= 1.8.11, < 2)
    faraday (2.13.1)
      faraday-net_http (>= 2.0, < 3.5)
      json
      logger
    faraday-net_http (3.4.1)
      net-http (>= 0.5.0)
    faraday-retry (2.3.2)
      faraday (~> 2.0)
    ffi (1.17.2-aarch64-linux-gnu)
    ffi (1.17.2-aarch64-linux-musl)
    ffi (1.17.2-arm-linux-gnu)
    ffi (1.17.2-arm-linux-musl)
    ffi (1.17.2-arm64-darwin)
    ffi (1.17.2-x86_64-linux-gnu)
    ffi (1.17.2-x86_64-linux-musl)
    fiddle (1.1.8)
    friendly_id (5.5.1)
      activerecord (>= 4.0.0)
    fugit (1.11.1)
      et-orbi (~> 1, >= 1.2.11)
      raabro (~> 1.4)
    gapic-common (1.0.0)
      faraday (>= 1.9, < 3.a)
      faraday-retry (>= 1.0, < 3.a)
      google-cloud-env (~> 2.2)
      google-logging-utils (~> 0.1)
      google-protobuf (>= 3.25, < 5.a)
      googleapis-common-protos (~> 1.6)
      googleapis-common-protos-types (~> 1.15)
      googleauth (~> 1.12)
      grpc (~> 1.66)
    globalid (1.2.1)
      activesupport (>= 6.1)
    google-analytics-data (0.7.1)
      google-analytics-data-v1beta (>= 0.11, < 2.a)
      google-cloud-core (~> 1.6)
    google-analytics-data-v1beta (0.17.0)
      gapic-common (~> 1.0)
      google-cloud-errors (~> 1.0)
    google-cloud-core (1.8.0)
      google-cloud-env (>= 1.0, < 3.a)
      google-cloud-errors (~> 1.0)
    google-cloud-env (2.3.1)
      base64 (~> 0.2)
      faraday (>= 1.0, < 3.a)
    google-cloud-errors (1.5.0)
    google-logging-utils (0.2.0)
    google-protobuf (4.31.1)
      bigdecimal
      rake (>= 13)
    google-protobuf (4.31.1-aarch64-linux-gnu)
      bigdecimal
      rake (>= 13)
    google-protobuf (4.31.1-aarch64-linux-musl)
      bigdecimal
      rake (>= 13)
    google-protobuf (4.31.1-arm64-darwin)
      bigdecimal
      rake (>= 13)
    google-protobuf (4.31.1-x86_64-linux-gnu)
      bigdecimal
      rake (>= 13)
    google-protobuf (4.31.1-x86_64-linux-musl)
      bigdecimal
      rake (>= 13)
    googleapis-common-protos (1.8.0)
      google-protobuf (>= 3.18, < 5.a)
      googleapis-common-protos-types (~> 1.20)
      grpc (~> 1.41)
    googleapis-common-protos-types (1.20.0)
      google-protobuf (>= 3.18, < 5.a)
    googleauth (1.14.0)
      faraday (>= 1.0, < 3.a)
      google-cloud-env (~> 2.2)
      google-logging-utils (~> 0.1)
      jwt (>= 1.4, < 3.0)
      multi_json (~> 1.11)
      os (>= 0.9, < 2.0)
      signet (>= 0.16, < 2.a)
    groupdate (6.7.0)
      activesupport (>= 7.1)
    grpc (1.73.0)
      google-protobuf (>= 3.25, < 5.0)
      googleapis-common-protos-types (~> 1.0)
    grpc (1.73.0-aarch64-linux)
      google-protobuf (>= 3.25, < 5.0)
      googleapis-common-protos-types (~> 1.0)
    grpc (1.73.0-arm64-darwin)
      google-protobuf (>= 3.25, < 5.0)
      googleapis-common-protos-types (~> 1.0)
    grpc (1.73.0-x86_64-linux)
      google-protobuf (>= 3.25, < 5.0)
      googleapis-common-protos-types (~> 1.0)
    hash_diff (1.1.1)
    hashdiff (1.2.0)
    http-accept (1.7.0)
    http-cookie (1.0.8)
      domain_name (~> 0.5)
    httparty (0.23.1)
      csv
      mini_mime (>= 1.0.0)
      multi_xml (>= 0.5.2)
    i18n (1.14.7)
      concurrent-ruby (~> 1.0)
    image_processing (1.14.0)
      mini_magick (>= 4.9.5, < 6)
      ruby-vips (>= 2.0.17, < 3)
    importmap-rails (2.1.0)
      actionpack (>= 6.0.0)
      activesupport (>= 6.0.0)
      railties (>= 6.0.0)
    io-console (0.8.0)
    irb (1.15.2)
      pp (>= 0.6.0)
      rdoc (>= 4.0.0)
      reline (>= 0.4.2)
    jbuilder (2.13.0)
      actionview (>= 5.0.0)
      activesupport (>= 5.0.0)
    json (2.12.2)
    jwt (2.10.1)
      base64
    kamal (2.7.0)
      activesupport (>= 7.0)
      base64 (~> 0.2)
      bcrypt_pbkdf (~> 1.0)
      concurrent-ruby (~> 1.2)
      dotenv (~> 3.1)
      ed25519 (~> 1.4)
      net-ssh (~> 7.3)
      sshkit (>= 1.23.0, < 2.0)
      thor (~> 1.3)
      zeitwerk (>= 2.6.18, < 3.0)
    kaminari (1.2.2)
      activesupport (>= 4.1.0)
      kaminari-actionview (= 1.2.2)
      kaminari-activerecord (= 1.2.2)
      kaminari-core (= 1.2.2)
    kaminari-actionview (1.2.2)
      actionview
      kaminari-core (= 1.2.2)
    kaminari-activerecord (1.2.2)
      activerecord
      kaminari-core (= 1.2.2)
    kaminari-core (1.2.2)
    language_server-protocol (********)
    launchy (3.1.1)
      addressable (~> 2.8)
      childprocess (~> 5.0)
      logger (~> 1.6)
    letter_opener (1.10.0)
      launchy (>= 2.2, < 4)
    lint_roller (1.1.0)
    listen (3.9.0)
      rb-fsevent (~> 0.10, >= 0.10.3)
      rb-inotify (~> 0.9, >= 0.9.10)
    lockbox (2.0.1)
    logger (1.7.0)
    loofah (2.24.1)
      crass (~> 1.0.2)
      nokogiri (>= 1.12.0)
    mail (2.8.1)
      mini_mime (>= 0.1.1)
      net-imap
      net-pop
      net-smtp
    mailchimp_api_v3 (0.2.18)
      rest-client (~> 2)
    marcel (1.0.4)
    memory_profiler (1.1.0)
    method_source (1.1.0)
    mime-types (3.7.0)
      logger
      mime-types-data (~> 3.2025, >= 3.2025.0507)
    mime-types-data (3.2025.0617)
    mini_magick (5.2.0)
      benchmark
      logger
    mini_mime (1.1.5)
    minitest (5.25.5)
    monetize (1.13.0)
      money (~> 6.12)
    money (6.19.0)
      i18n (>= 0.6.4, <= 2)
    money-rails (1.15.0)
      activesupport (>= 3.0)
      monetize (~> 1.9)
      money (~> 6.13)
      railties (>= 3.0)
    msgpack (1.8.0)
    multi_json (1.15.0)
    multi_xml (0.7.2)
      bigdecimal (~> 3.1)
    net-http (0.6.0)
      uri
    net-imap (0.5.8)
      date
      net-protocol
    net-pop (0.1.2)
      net-protocol
    net-protocol (0.2.2)
      timeout
    net-scp (4.1.0)
      net-ssh (>= 2.6.5, < 8.0.0)
    net-sftp (4.0.0)
      net-ssh (>= 5.0.0, < 8.0.0)
    net-smtp (0.5.1)
      net-protocol
    net-ssh (7.3.0)
    netrc (0.11.0)
    nio4r (2.7.4)
    nokogiri (1.18.8-aarch64-linux-gnu)
      racc (~> 1.4)
    nokogiri (1.18.8-aarch64-linux-musl)
      racc (~> 1.4)
    nokogiri (1.18.8-arm-linux-gnu)
      racc (~> 1.4)
    nokogiri (1.18.8-arm-linux-musl)
      racc (~> 1.4)
    nokogiri (1.18.8-arm64-darwin)
      racc (~> 1.4)
    nokogiri (1.18.8-x86_64-linux-gnu)
      racc (~> 1.4)
    nokogiri (1.18.8-x86_64-linux-musl)
      racc (~> 1.4)
    oj (3.16.11)
      bigdecimal (>= 3.0)
      ostruct (>= 0.2)
    openssl (3.3.0)
    opentelemetry-api (1.5.0)
    opentelemetry-common (0.22.0)
      opentelemetry-api (~> 1.0)
    opentelemetry-exporter-otlp (0.30.0)
      google-protobuf (>= 3.18)
      googleapis-common-protos-types (~> 1.3)
      opentelemetry-api (~> 1.1)
      opentelemetry-common (~> 0.20)
      opentelemetry-sdk (~> 1.2)
      opentelemetry-semantic_conventions
    opentelemetry-helpers-mysql (0.2.0)
      opentelemetry-api (~> 1.0)
      opentelemetry-common (~> 0.21)
    opentelemetry-helpers-sql (0.1.1)
      opentelemetry-api (~> 1.0)
    opentelemetry-helpers-sql-obfuscation (0.3.0)
      opentelemetry-common (~> 0.21)
    opentelemetry-instrumentation-action_mailer (0.4.0)
      opentelemetry-api (~> 1.0)
      opentelemetry-instrumentation-active_support (~> 0.7)
      opentelemetry-instrumentation-base (~> 0.23.0)
    opentelemetry-instrumentation-action_pack (0.12.3)
      opentelemetry-api (~> 1.0)
      opentelemetry-instrumentation-base (~> 0.23.0)
      opentelemetry-instrumentation-rack (~> 0.21)
    opentelemetry-instrumentation-action_view (0.9.0)
      opentelemetry-api (~> 1.0)
      opentelemetry-instrumentation-active_support (~> 0.7)
      opentelemetry-instrumentation-base (~> 0.23.0)
    opentelemetry-instrumentation-active_job (0.8.0)
      opentelemetry-api (~> 1.0)
      opentelemetry-instrumentation-base (~> 0.23.0)
    opentelemetry-instrumentation-active_model_serializers (0.22.0)
      opentelemetry-api (~> 1.0)
      opentelemetry-instrumentation-active_support (>= 0.7.0)
      opentelemetry-instrumentation-base (~> 0.23.0)
    opentelemetry-instrumentation-active_record (0.9.0)
      opentelemetry-api (~> 1.0)
      opentelemetry-instrumentation-base (~> 0.23.0)
    opentelemetry-instrumentation-active_storage (0.1.1)
      opentelemetry-api (~> 1.0)
      opentelemetry-instrumentation-active_support (~> 0.7)
      opentelemetry-instrumentation-base (~> 0.23.0)
    opentelemetry-instrumentation-active_support (0.8.0)
      opentelemetry-api (~> 1.0)
      opentelemetry-instrumentation-base (~> 0.23.0)
    opentelemetry-instrumentation-all (0.78.0)
      opentelemetry-instrumentation-active_model_serializers (~> 0.22.0)
      opentelemetry-instrumentation-aws_lambda (~> 0.3.0)
      opentelemetry-instrumentation-aws_sdk (~> 0.8.0)
      opentelemetry-instrumentation-bunny (~> 0.22.0)
      opentelemetry-instrumentation-concurrent_ruby (~> 0.22.0)
      opentelemetry-instrumentation-dalli (~> 0.27.0)
      opentelemetry-instrumentation-delayed_job (~> 0.23.0)
      opentelemetry-instrumentation-ethon (~> 0.22.0)
      opentelemetry-instrumentation-excon (~> 0.23.0)
      opentelemetry-instrumentation-faraday (~> 0.27.0)
      opentelemetry-instrumentation-grape (~> 0.3.0)
      opentelemetry-instrumentation-graphql (~> 0.29.0)
      opentelemetry-instrumentation-grpc (~> 0.2.0)
      opentelemetry-instrumentation-gruf (~> 0.3.0)
      opentelemetry-instrumentation-http (~> 0.25.0)
      opentelemetry-instrumentation-http_client (~> 0.23.0)
      opentelemetry-instrumentation-koala (~> 0.21.0)
      opentelemetry-instrumentation-lmdb (~> 0.23.0)
      opentelemetry-instrumentation-mongo (~> 0.23.0)
      opentelemetry-instrumentation-mysql2 (~> 0.29.0)
      opentelemetry-instrumentation-net_http (~> 0.23.0)
      opentelemetry-instrumentation-pg (~> 0.30.0)
      opentelemetry-instrumentation-que (~> 0.9.0)
      opentelemetry-instrumentation-racecar (~> 0.4.0)
      opentelemetry-instrumentation-rack (~> 0.26.0)
      opentelemetry-instrumentation-rails (~> 0.36.0)
      opentelemetry-instrumentation-rake (~> 0.3.1)
      opentelemetry-instrumentation-rdkafka (~> 0.7.0)
      opentelemetry-instrumentation-redis (~> 0.26.1)
      opentelemetry-instrumentation-resque (~> 0.6.0)
      opentelemetry-instrumentation-restclient (~> 0.23.0)
      opentelemetry-instrumentation-ruby_kafka (~> 0.22.0)
      opentelemetry-instrumentation-sidekiq (~> 0.26.0)
      opentelemetry-instrumentation-sinatra (~> 0.25.0)
      opentelemetry-instrumentation-trilogy (~> 0.61.0)
    opentelemetry-instrumentation-aws_lambda (0.3.0)
      opentelemetry-api (~> 1.0)
      opentelemetry-instrumentation-base (~> 0.23.0)
    opentelemetry-instrumentation-aws_sdk (0.8.1)
      opentelemetry-api (~> 1.0)
      opentelemetry-instrumentation-base (~> 0.23.0)
    opentelemetry-instrumentation-base (0.23.0)
      opentelemetry-api (~> 1.0)
      opentelemetry-common (~> 0.21)
      opentelemetry-registry (~> 0.1)
    opentelemetry-instrumentation-bunny (0.22.0)
      opentelemetry-api (~> 1.0)
      opentelemetry-instrumentation-base (~> 0.23.0)
    opentelemetry-instrumentation-concurrent_ruby (0.22.0)
      opentelemetry-api (~> 1.0)
      opentelemetry-instrumentation-base (~> 0.23.0)
    opentelemetry-instrumentation-dalli (0.27.3)
      opentelemetry-api (~> 1.0)
      opentelemetry-instrumentation-base (~> 0.23.0)
    opentelemetry-instrumentation-delayed_job (0.23.0)
      opentelemetry-api (~> 1.0)
      opentelemetry-instrumentation-base (~> 0.23.0)
    opentelemetry-instrumentation-ethon (0.22.0)
      opentelemetry-api (~> 1.0)
      opentelemetry-instrumentation-base (~> 0.23.0)
    opentelemetry-instrumentation-excon (0.23.0)
      opentelemetry-api (~> 1.0)
      opentelemetry-instrumentation-base (~> 0.23.0)
    opentelemetry-instrumentation-faraday (0.27.0)
      opentelemetry-api (~> 1.0)
      opentelemetry-instrumentation-base (~> 0.23.0)
    opentelemetry-instrumentation-grape (0.3.0)
      opentelemetry-api (~> 1.0)
      opentelemetry-instrumentation-base (~> 0.23.0)
      opentelemetry-instrumentation-rack (~> 0.21)
    opentelemetry-instrumentation-graphql (0.29.0)
      opentelemetry-api (~> 1.0)
      opentelemetry-instrumentation-base (~> 0.23.0)
    opentelemetry-instrumentation-grpc (0.2.1)
      opentelemetry-api (~> 1.0)
      opentelemetry-instrumentation-base (~> 0.23.0)
    opentelemetry-instrumentation-gruf (0.3.0)
      opentelemetry-api (>= 1.0.0)
      opentelemetry-instrumentation-base (~> 0.23.0)
    opentelemetry-instrumentation-http (0.25.1)
      opentelemetry-api (~> 1.0)
      opentelemetry-instrumentation-base (~> 0.23.0)
    opentelemetry-instrumentation-http_client (0.23.0)
      opentelemetry-api (~> 1.0)
      opentelemetry-instrumentation-base (~> 0.23.0)
    opentelemetry-instrumentation-koala (0.21.0)
      opentelemetry-api (~> 1.0)
      opentelemetry-instrumentation-base (~> 0.23.0)
    opentelemetry-instrumentation-lmdb (0.23.0)
      opentelemetry-api (~> 1.0)
      opentelemetry-instrumentation-base (~> 0.23.0)
    opentelemetry-instrumentation-mongo (0.23.0)
      opentelemetry-api (~> 1.0)
      opentelemetry-instrumentation-base (~> 0.23.0)
    opentelemetry-instrumentation-mysql2 (0.29.1)
      opentelemetry-api (~> 1.0)
      opentelemetry-helpers-mysql
      opentelemetry-helpers-sql
      opentelemetry-helpers-sql-obfuscation
      opentelemetry-instrumentation-base (~> 0.23.0)
    opentelemetry-instrumentation-net_http (0.23.0)
      opentelemetry-api (~> 1.0)
      opentelemetry-instrumentation-base (~> 0.23.0)
    opentelemetry-instrumentation-pg (0.30.1)
      opentelemetry-api (~> 1.0)
      opentelemetry-helpers-sql
      opentelemetry-helpers-sql-obfuscation
      opentelemetry-instrumentation-base (~> 0.23.0)
    opentelemetry-instrumentation-que (0.9.0)
      opentelemetry-api (~> 1.0)
      opentelemetry-instrumentation-base (~> 0.23.0)
    opentelemetry-instrumentation-racecar (0.4.0)
      opentelemetry-api (~> 1.0)
      opentelemetry-instrumentation-base (~> 0.23.0)
    opentelemetry-instrumentation-rack (0.26.0)
      opentelemetry-api (~> 1.0)
      opentelemetry-instrumentation-base (~> 0.23.0)
    opentelemetry-instrumentation-rails (0.36.0)
      opentelemetry-api (~> 1.0)
      opentelemetry-instrumentation-action_mailer (~> 0.4.0)
      opentelemetry-instrumentation-action_pack (~> 0.12.0)
      opentelemetry-instrumentation-action_view (~> 0.9.0)
      opentelemetry-instrumentation-active_job (~> 0.8.0)
      opentelemetry-instrumentation-active_record (~> 0.9.0)
      opentelemetry-instrumentation-active_storage (~> 0.1.0)
      opentelemetry-instrumentation-active_support (~> 0.8.0)
      opentelemetry-instrumentation-base (~> 0.23.0)
      opentelemetry-instrumentation-concurrent_ruby (~> 0.22.0)
    opentelemetry-instrumentation-rake (0.3.1)
      opentelemetry-api (~> 1.0)
      opentelemetry-instrumentation-base (~> 0.23.0)
    opentelemetry-instrumentation-rdkafka (0.7.0)
      opentelemetry-api (~> 1.0)
      opentelemetry-instrumentation-base (~> 0.23.0)
    opentelemetry-instrumentation-redis (0.26.1)
      opentelemetry-api (~> 1.0)
      opentelemetry-instrumentation-base (~> 0.23.0)
    opentelemetry-instrumentation-resque (0.6.0)
      opentelemetry-api (~> 1.0)
      opentelemetry-instrumentation-base (~> 0.23.0)
    opentelemetry-instrumentation-restclient (0.23.0)
      opentelemetry-api (~> 1.0)
      opentelemetry-instrumentation-base (~> 0.23.0)
    opentelemetry-instrumentation-ruby_kafka (0.22.0)
      opentelemetry-api (~> 1.0)
      opentelemetry-instrumentation-base (~> 0.23.0)
    opentelemetry-instrumentation-sidekiq (0.26.1)
      opentelemetry-api (~> 1.0)
      opentelemetry-instrumentation-base (~> 0.23.0)
    opentelemetry-instrumentation-sinatra (0.25.0)
      opentelemetry-api (~> 1.0)
      opentelemetry-instrumentation-base (~> 0.23.0)
      opentelemetry-instrumentation-rack (~> 0.21)
    opentelemetry-instrumentation-trilogy (0.61.1)
      opentelemetry-api (~> 1.0)
      opentelemetry-helpers-mysql
      opentelemetry-helpers-sql
      opentelemetry-helpers-sql-obfuscation
      opentelemetry-instrumentation-base (~> 0.23.0)
      opentelemetry-semantic_conventions (>= 1.8.0)
    opentelemetry-registry (0.4.0)
      opentelemetry-api (~> 1.1)
    opentelemetry-sdk (1.8.0)
      opentelemetry-api (~> 1.1)
      opentelemetry-common (~> 0.20)
      opentelemetry-registry (~> 0.2)
      opentelemetry-semantic_conventions
    opentelemetry-semantic_conventions (1.11.0)
      opentelemetry-api (~> 1.0)
    orm_adapter (0.5.0)
    os (1.1.4)
    ostruct (0.6.2)
    paper_trail (16.0.0)
      activerecord (>= 6.1)
      request_store (~> 1.4)
    parallel (1.27.0)
    paranoia (3.0.1)
      activerecord (>= 6, < 8.1)
    parser (3.3.8.0)
      ast (~> 2.4.1)
      racc
    pg (1.5.9)
    pp (0.6.2)
      prettyprint
    prettyprint (0.2.0)
    prism (1.4.0)
    psych (5.2.6)
      date
      stringio
    public_suffix (6.0.2)
    puma (6.6.0)
      nio4r (~> 2.0)
    pundit (2.5.0)
      activesupport (>= 3.0.0)
    raabro (1.4.0)
    racc (1.8.1)
    rack (3.1.16)
    rack-attack (6.7.0)
      rack (>= 1.0, < 4)
    rack-cors (3.0.0)
      logger
      rack (>= 3.0.14)
    rack-mini-profiler (4.0.0)
      rack (>= 1.2.0)
    rack-session (2.1.1)
      base64 (>= 0.1.0)
      rack (>= 3.0.0)
    rack-test (2.2.0)
      rack (>= 1.3)
    rackup (2.2.1)
      rack (>= 3)
    rails (8.0.2)
      actioncable (= 8.0.2)
      actionmailbox (= 8.0.2)
      actionmailer (= 8.0.2)
      actionpack (= 8.0.2)
      actiontext (= 8.0.2)
      actionview (= 8.0.2)
      activejob (= 8.0.2)
      activemodel (= 8.0.2)
      activerecord (= 8.0.2)
      activestorage (= 8.0.2)
      activesupport (= 8.0.2)
      bundler (>= 1.15.0)
      railties (= 8.0.2)
    rails-controller-testing (1.0.5)
      actionpack (>= 5.0.1.rc1)
      actionview (>= 5.0.1.rc1)
      activesupport (>= 5.0.1.rc1)
    rails-dom-testing (2.3.0)
      activesupport (>= 5.0.0)
      minitest
      nokogiri (>= 1.6)
    rails-html-sanitizer (1.6.2)
      loofah (~> 2.21)
      nokogiri (>= 1.15.7, != 1.16.7, != 1.16.6, != 1.16.5, != 1.16.4, != 1.16.3, != 1.16.2, != 1.16.1, != 1.16.0.rc1, != 1.16.0)
    railties (8.0.2)
      actionpack (= 8.0.2)
      activesupport (= 8.0.2)
      irb (~> 1.13)
      rackup (>= 1.0.0)
      rake (>= 12.2)
      thor (~> 1.0, >= 1.2.2)
      zeitwerk (~> 2.6)
    rainbow (3.1.1)
    rake (13.3.0)
    ransack (4.3.0)
      activerecord (>= 6.1.5)
      activesupport (>= 6.1.5)
      i18n
    rb-fsevent (0.11.2)
    rb-inotify (0.11.1)
      ffi (~> 1.0)
    rbnacl (7.1.2)
      ffi (~> 1)
    rdoc (6.14.1)
      erb
      psych (>= 4.0.0)
    regexp_parser (2.10.0)
    reline (0.6.1)
      io-console (~> 0.5)
    request_store (1.7.0)
      rack (>= 1.4)
    responders (3.1.1)
      actionpack (>= 5.2)
      railties (>= 5.2)
    rest-client (2.1.0)
      http-accept (>= 1.7.0, < 2.0)
      http-cookie (>= 1.0.2, < 2.0)
      mime-types (>= 1.16, < 4.0)
      netrc (~> 0.8)
    rexml (3.4.1)
    roo (2.10.1)
      nokogiri (~> 1)
      rubyzip (>= 1.3.0, < 3.0.0)
    roo-xls (1.2.0)
      nokogiri
      roo (>= 2.0.0, < 3)
      spreadsheet (> 0.9.0)
    rspec-core (3.13.4)
      rspec-support (~> 3.13.0)
    rspec-expectations (3.13.5)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-mocks (3.13.5)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-rails (8.0.0)
      actionpack (>= 7.2)
      activesupport (>= 7.2)
      railties (>= 7.2)
      rspec-core (~> 3.13)
      rspec-expectations (~> 3.13)
      rspec-mocks (~> 3.13)
      rspec-support (~> 3.13)
    rspec-support (3.13.4)
    rubocop (1.76.2)
      json (~> 2.3)
      language_server-protocol (~> ********)
      lint_roller (~> 1.1.0)
      parallel (~> 1.10)
      parser (>= *******)
      rainbow (>= 2.2.2, < 4.0)
      regexp_parser (>= 2.9.3, < 3.0)
      rubocop-ast (>= 1.45.1, < 2.0)
      ruby-progressbar (~> 1.7)
      unicode-display_width (>= 2.4.0, < 4.0)
    rubocop-ast (1.45.1)
      parser (>= 3.3.7.2)
      prism (~> 1.4)
    rubocop-performance (1.25.0)
      lint_roller (~> 1.1)
      rubocop (>= 1.75.0, < 2.0)
      rubocop-ast (>= 1.38.0, < 2.0)
    rubocop-rails (2.32.0)
      activesupport (>= 4.2.0)
      lint_roller (~> 1.1)
      rack (>= 1.1)
      rubocop (>= 1.75.0, < 2.0)
      rubocop-ast (>= 1.44.0, < 2.0)
    rubocop-rails-omakase (1.1.0)
      rubocop (>= 1.72)
      rubocop-performance (>= 1.24)
      rubocop-rails (>= 2.30)
    ruby-ole (********)
    ruby-progressbar (1.13.0)
    ruby-vips (2.2.4)
      ffi (~> 1.12)
      logger
    rubyzip (2.4.1)
    securerandom (0.4.1)
    shopify_api (14.10.0)
      activesupport
      concurrent-ruby
      hash_diff
      httparty
      jwt
      oj
      openssl
      securerandom
      sorbet-runtime
      zeitwerk (~> 2.5)
    shoulda-matchers (6.5.0)
      activesupport (>= 5.2.0)
    signet (0.20.0)
      addressable (~> 2.8)
      faraday (>= 0.17.5, < 3.a)
      jwt (>= 1.5, < 3.0)
      multi_json (~> 1.10)
    simplecov (0.22.0)
      docile (~> 1.1)
      simplecov-html (~> 0.11)
      simplecov_json_formatter (~> 0.1)
    simplecov-html (0.13.1)
    simplecov_json_formatter (0.1.4)
    smarter_csv (1.14.4)
    solid_cable (3.0.8)
      actioncable (>= 7.2)
      activejob (>= 7.2)
      activerecord (>= 7.2)
      railties (>= 7.2)
    solid_cache (1.0.7)
      activejob (>= 7.2)
      activerecord (>= 7.2)
      railties (>= 7.2)
    solid_queue (1.1.5)
      activejob (>= 7.1)
      activerecord (>= 7.1)
      concurrent-ruby (>= 1.3.1)
      fugit (~> 1.11.0)
      railties (>= 7.1)
      thor (~> 1.3.1)
    sorbet-runtime (0.5.12194)
    spreadsheet (1.3.4)
      bigdecimal
      logger
      ruby-ole
    spring (4.3.0)
    spring-watcher-listen (2.1.0)
      listen (>= 2.7, < 4.0)
      spring (>= 4)
    sprockets (4.2.2)
      concurrent-ruby (~> 1.0)
      logger
      rack (>= 2.2.4, < 4)
    sprockets-rails (3.5.2)
      actionpack (>= 6.1)
      activesupport (>= 6.1)
      sprockets (>= 3.0.0)
    sshkit (1.24.0)
      base64
      logger
      net-scp (>= 1.1.2)
      net-sftp (>= 2.1.2)
      net-ssh (>= 2.8.0)
      ostruct
    stackprof (0.2.27)
    stimulus-rails (1.3.4)
      railties (>= 6.0.0)
    stringio (3.1.7)
    stripe (15.2.1)
    tailwindcss-rails (4.2.3)
      railties (>= 7.0.0)
      tailwindcss-ruby (~> 4.0)
    tailwindcss-ruby (4.1.10)
    tailwindcss-ruby (4.1.10-aarch64-linux-gnu)
    tailwindcss-ruby (4.1.10-aarch64-linux-musl)
    tailwindcss-ruby (4.1.10-arm64-darwin)
    tailwindcss-ruby (4.1.10-x86_64-linux-gnu)
    tailwindcss-ruby (4.1.10-x86_64-linux-musl)
    thor (1.3.2)
    thruster (0.1.14)
    thruster (0.1.14-aarch64-linux)
    thruster (0.1.14-arm64-darwin)
    thruster (0.1.14-x86_64-linux)
    timecop (0.9.10)
    timeout (0.4.3)
    turbo-rails (2.0.16)
      actionpack (>= 7.1.0)
      railties (>= 7.1.0)
    tzinfo (2.0.6)
      concurrent-ruby (~> 1.0)
    unicode-display_width (3.1.4)
      unicode-emoji (~> 4.0, >= 4.0.4)
    unicode-emoji (4.0.4)
    uniform_notifier (1.17.0)
    uri (1.0.3)
    useragent (0.16.11)
    validate_url (1.0.15)
      activemodel (>= 3.0.0)
      public_suffix
    vcr (6.3.1)
      base64
    view_component (3.23.2)
      activesupport (>= 5.2.0, < 8.1)
      concurrent-ruby (~> 1)
      method_source (~> 1.0)
    warden (1.2.9)
      rack (>= 2.0.9)
    webmock (3.25.1)
      addressable (>= 2.8.0)
      crack (>= 0.3.2)
      hashdiff (>= 0.4.0, < 2.0.0)
    websocket-driver (0.8.0)
      base64
      websocket-extensions (>= 0.1.0)
    websocket-extensions (0.1.5)
    zeitwerk (2.7.3)

PLATFORMS
  aarch64-linux
  aarch64-linux-gnu
  aarch64-linux-musl
  arm-linux-gnu
  arm-linux-musl
  arm64-darwin-24
  x86_64-linux
  x86_64-linux-gnu
  x86_64-linux-musl

DEPENDENCIES
  activerecord-import
  annotate
  bcrypt (~> 3.1.7)
  blind_index
  bootsnap
  brakeman
  bullet
  chartkick
  chronic
  creek
  database_cleaner-active_record
  debug
  devise
  factory_bot_rails
  faker
  faraday
  faraday-retry
  friendly_id
  fugit
  google-analytics-data
  groupdate
  httparty
  image_processing (~> 1.2)
  importmap-rails
  jbuilder
  jwt
  kamal
  kaminari
  letter_opener
  listen
  lockbox
  mailchimp_api_v3
  memory_profiler
  money-rails
  opentelemetry-exporter-otlp
  opentelemetry-instrumentation-all
  opentelemetry-sdk
  paper_trail
  paranoia
  pg (~> 1.1)
  puma (>= 5.0)
  pundit
  rack-attack
  rack-cors
  rack-mini-profiler
  rails (~> 8.0.2)
  rails-controller-testing
  ransack
  rbnacl
  request_store
  roo
  roo-xls
  rspec-rails
  rubocop-rails-omakase
  shopify_api
  shoulda-matchers
  simplecov
  smarter_csv
  solid_cable
  solid_cache
  solid_queue
  spring
  spring-watcher-listen
  sprockets-rails
  stackprof
  stimulus-rails
  stripe
  tailwindcss-rails
  thruster
  timecop
  turbo-rails
  tzinfo-data
  validate_url
  vcr
  view_component
  webmock

BUNDLED WITH
   2.6.9
