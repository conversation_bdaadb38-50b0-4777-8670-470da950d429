<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Data Refinery Platform - Data Quality</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/feather-icons/4.29.0/feather.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        :root {
            --bg-cream: #EAE4D5;
            --teal-primary: #14B8A6;
            --teal-dark: #0D9488;
            --teal-light: #5EEAD4;
            --teal-50: #F0FDFA;
            --gray-800: #1F2937;
            --gray-600: #4B5563;
            --gray-400: #9CA3AF;
            --success: #10B981;
            --warning: #F59E0B;
            --error: #EF4444;
            --info: #3B82F6;
        }
        
        body {
            background-color: var(--bg-cream);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        
        .glassmorphism {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.4);
            box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.08);
        }
        
        .glassmorphism-dark {
            background: rgba(31, 41, 55, 0.95);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            color: white;
        }
        
        .teal-gradient {
            background: linear-gradient(135deg, var(--teal-primary) 0%, var(--teal-dark) 100%);
        }
        
        /* Quality Score Ring */
        .quality-ring {
            transform: rotate(-90deg);
        }
        
        .quality-ring-circle {
            transition: stroke-dashoffset 0.8s ease;
        }
        
        /* Score colors */
        .score-excellent { color: var(--success); }
        .score-good { color: var(--teal-primary); }
        .score-fair { color: var(--warning); }
        .score-poor { color: var(--error); }
        
        /* Issue severity badges */
        .severity-critical {
            background: rgba(239, 68, 68, 0.1);
            color: var(--error);
            border: 1px solid rgba(239, 68, 68, 0.2);
        }
        
        .severity-major {
            background: rgba(245, 158, 11, 0.1);
            color: var(--warning);
            border: 1px solid rgba(245, 158, 11, 0.2);
        }
        
        .severity-minor {
            background: rgba(59, 130, 246, 0.1);
            color: var(--info);
            border: 1px solid rgba(59, 130, 246, 0.2);
        }
        
        /* Rule status */
        .rule-status {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            position: relative;
        }
        
        .rule-status::after {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            border-radius: 50%;
            background: inherit;
            opacity: 0.3;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0%, 100% { 
                opacity: 0.3;
                transform: scale(1);
            }
            50% { 
                opacity: 0.1;
                transform: scale(1.2);
            }
        }
        
        /* Heatmap styles */
        .heatmap-cell {
            position: relative;
            transition: all 0.2s;
            cursor: pointer;
        }
        
        .heatmap-cell:hover {
            transform: scale(1.1);
            z-index: 10;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }
        
        .heatmap-excellent { background: #10B981; }
        .heatmap-good { background: #14B8A6; }
        .heatmap-fair { background: #F59E0B; }
        .heatmap-poor { background: #EF4444; }
        
        /* Timeline styles */
        .timeline-point {
            position: relative;
            cursor: pointer;
        }
        
        .timeline-point:hover .timeline-tooltip {
            opacity: 1;
            visibility: visible;
        }
        
        .timeline-tooltip {
            position: absolute;
            bottom: 100%;
            left: 50%;
            transform: translateX(-50%);
            background: var(--gray-800);
            color: white;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 12px;
            white-space: nowrap;
            opacity: 0;
            visibility: hidden;
            transition: all 0.2s;
            margin-bottom: 8px;
        }
        
        .timeline-tooltip::after {
            content: '';
            position: absolute;
            top: 100%;
            left: 50%;
            transform: translateX(-50%);
            border: 6px solid transparent;
            border-top-color: var(--gray-800);
        }
        
        /* Progress animations */
        .progress-fill {
            animation: progressFill 1.5s ease-out;
        }
        
        @keyframes progressFill {
            from {
                width: 0;
            }
        }
        
        /* Animations */
        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        
        .animate-slide-in {
            animation: slideIn 0.5s ease-out;
        }
        
        .animate-fade-in {
            animation: fadeIn 0.3s ease-out;
        }
        
        /* Toggle Switch */
        .toggle-switch {
            position: relative;
            width: 44px;
            height: 24px;
            background: var(--gray-400);
            border-radius: 12px;
            cursor: pointer;
            transition: background 0.3s;
        }
        
        .toggle-switch.active {
            background: var(--teal-primary);
        }
        
        .toggle-switch::after {
            content: '';
            position: absolute;
            top: 2px;
            left: 2px;
            width: 20px;
            height: 20px;
            background: white;
            border-radius: 50%;
            transition: transform 0.3s;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }
        
        .toggle-switch.active::after {
            transform: translateX(20px);
        }
        
        /* Custom Scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }
        
        ::-webkit-scrollbar-track {
            background: rgba(0, 0, 0, 0.05);
            border-radius: 4px;
        }
        
        ::-webkit-scrollbar-thumb {
            background: var(--teal-primary);
            border-radius: 4px;
        }
        
        ::-webkit-scrollbar-thumb:hover {
            background: var(--teal-dark);
        }
        
        /* Rule Builder */
        .rule-builder {
            background: #f8fafc;
            border: 2px dashed #cbd5e1;
            border-radius: 8px;
            padding: 24px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .rule-builder:hover {
            border-color: var(--teal-primary);
            background: var(--teal-50);
        }
    </style>
</head>
<body class="min-h-screen">
    <!-- Navigation Header -->
    <nav class="fixed top-0 left-0 right-0 z-50 glassmorphism">
        <div class="container mx-auto px-6 py-3">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-8">
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 teal-gradient rounded-lg flex items-center justify-center shadow-lg">
                            <i data-feather="database" class="w-6 h-6 text-white"></i>
                        </div>
                        <h1 class="text-2xl font-bold text-gray-800">DataRefinery</h1>
                    </div>
                    <div class="hidden lg:flex items-center space-x-6">
                        <a href="#" class="text-gray-600 hover:text-teal-600 font-medium transition-colors">Dashboard</a>
                        <a href="#" class="text-gray-600 hover:text-teal-600 font-medium transition-colors">Data Sources</a>
                        <a href="#" class="text-gray-600 hover:text-teal-600 font-medium transition-colors">Pipelines</a>
                        <a href="#" class="text-gray-600 hover:text-teal-600 font-medium transition-colors">Monitoring</a>
                        <a href="#" class="text-teal-600 font-medium">Data Quality</a>
                        <a href="#" class="text-gray-600 hover:text-teal-600 font-medium transition-colors">Analytics</a>
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <button class="p-2 rounded-lg hover:bg-gray-100 transition-colors">
                        <i data-feather="bell" class="w-5 h-5 text-gray-600"></i>
                    </button>
                    <div class="flex items-center space-x-3 pl-4 border-l border-gray-200">
                        <div class="hidden md:text-right">
                            <p class="text-sm font-medium text-gray-800">John Doe</p>
                            <p class="text-xs text-gray-500">Premium Plan</p>
                        </div>
                        <div class="w-10 h-10 bg-teal-100 rounded-full flex items-center justify-center">
                            <span class="text-teal-600 font-semibold">JD</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="pt-20 pb-8">
        <div class="container mx-auto px-6 py-8">
            <!-- Page Header -->
            <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-8">
                <div class="mb-4 lg:mb-0">
                    <h2 class="text-3xl font-bold text-gray-800 mb-2">Data Quality Management</h2>
                    <p class="text-gray-600">Monitor and improve data quality across all sources and pipelines</p>
                </div>
                <div class="flex flex-wrap gap-3">
                    <button class="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg font-medium hover:bg-gray-200 transition-colors flex items-center space-x-2">
                        <i data-feather="settings" class="w-4 h-4"></i>
                        <span>Configure Rules</span>
                    </button>
                    <button class="px-4 py-2 bg-purple-100 text-purple-700 rounded-lg font-medium hover:bg-purple-200 transition-colors flex items-center space-x-2">
                        <i data-feather="cpu" class="w-4 h-4"></i>
                        <span>AI Suggestions</span>
                    </button>
                    <button class="px-4 py-2 teal-gradient text-white rounded-lg font-medium hover:opacity-90 transition-opacity flex items-center space-x-2">
                        <i data-feather="play-circle" class="w-4 h-4"></i>
                        <span>Run Quality Check</span>
                    </button>
                </div>
            </div>

            <!-- Overall Quality Score -->
            <div class="glassmorphism rounded-xl p-8 mb-8 animate-slide-in">
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                    <!-- Score Display -->
                    <div class="text-center lg:text-left">
                        <h3 class="text-lg font-semibold text-gray-800 mb-6">Overall Data Quality Score</h3>
                        <div class="relative inline-block">
                            <svg class="quality-ring w-40 h-40">
                                <circle cx="80" cy="80" r="70" stroke="#e5e7eb" stroke-width="12" fill="none"></circle>
                                <circle class="quality-ring-circle" cx="80" cy="80" r="70" 
                                        stroke="#10B981" stroke-width="12" fill="none" 
                                        stroke-dasharray="440" stroke-dashoffset="26" stroke-linecap="round"></circle>
                            </svg>
                            <div class="absolute inset-0 flex flex-col items-center justify-center">
                                <span class="text-5xl font-bold text-gray-800">94</span>
                                <span class="text-lg text-gray-600">Excellent</span>
                            </div>
                        </div>
                        <p class="text-sm text-gray-600 mt-4">Last checked: 10 minutes ago</p>
                    </div>
                    
                    <!-- Dimension Scores -->
                    <div class="lg:col-span-2">
                        <h4 class="text-sm font-semibold text-gray-700 uppercase tracking-wider mb-4">Quality Dimensions</h4>
                        <div class="grid grid-cols-2 gap-6">
                            <div>
                                <div class="flex items-center justify-between mb-2">
                                    <span class="text-sm text-gray-600">Completeness</span>
                                    <span class="text-sm font-semibold score-excellent">98%</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-2">
                                    <div class="bg-green-500 h-2 rounded-full progress-fill" style="width: 98%"></div>
                                </div>
                                <p class="text-xs text-gray-500 mt-1">2,450 of 2,500 fields complete</p>
                            </div>
                            
                            <div>
                                <div class="flex items-center justify-between mb-2">
                                    <span class="text-sm text-gray-600">Accuracy</span>
                                    <span class="text-sm font-semibold score-excellent">95%</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-2">
                                    <div class="bg-green-500 h-2 rounded-full progress-fill" style="width: 95%"></div>
                                </div>
                                <p class="text-xs text-gray-500 mt-1">237,500 of 250,000 records valid</p>
                            </div>
                            
                            <div>
                                <div class="flex items-center justify-between mb-2">
                                    <span class="text-sm text-gray-600">Consistency</span>
                                    <span class="text-sm font-semibold score-good">88%</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-2">
                                    <div class="bg-teal-500 h-2 rounded-full progress-fill" style="width: 88%"></div>
                                </div>
                                <p class="text-xs text-gray-500 mt-1">Format variations in 3 fields</p>
                            </div>
                            
                            <div>
                                <div class="flex items-center justify-between mb-2">
                                    <span class="text-sm text-gray-600">Timeliness</span>
                                    <span class="text-sm font-semibold score-excellent">96%</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-2">
                                    <div class="bg-green-500 h-2 rounded-full progress-fill" style="width: 96%"></div>
                                </div>
                                <p class="text-xs text-gray-500 mt-1">All sources synced within SLA</p>
                            </div>
                            
                            <div>
                                <div class="flex items-center justify-between mb-2">
                                    <span class="text-sm text-gray-600">Uniqueness</span>
                                    <span class="text-sm font-semibold score-excellent">99.5%</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-2">
                                    <div class="bg-green-500 h-2 rounded-full progress-fill" style="width: 99.5%"></div>
                                </div>
                                <p class="text-xs text-gray-500 mt-1">125 duplicate records found</p>
                            </div>
                            
                            <div>
                                <div class="flex items-center justify-between mb-2">
                                    <span class="text-sm text-gray-600">Validity</span>
                                    <span class="text-sm font-semibold score-good">92%</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-2">
                                    <div class="bg-teal-500 h-2 rounded-full progress-fill" style="width: 92%"></div>
                                </div>
                                <p class="text-xs text-gray-500 mt-1">All constraints satisfied</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions & Stats -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
                <div class="glassmorphism rounded-lg p-4 animate-slide-in" style="animation-delay: 0.1s">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-2xl font-bold text-gray-800">23</p>
                            <p class="text-sm text-gray-600">Active Rules</p>
                        </div>
                        <div class="w-12 h-12 bg-teal-100 rounded-lg flex items-center justify-center">
                            <i data-feather="shield" class="w-6 h-6 text-teal-600"></i>
                        </div>
                    </div>
                </div>
                
                <div class="glassmorphism rounded-lg p-4 animate-slide-in" style="animation-delay: 0.2s">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-2xl font-bold text-gray-800">156</p>
                            <p class="text-sm text-gray-600">Issues Found</p>
                        </div>
                        <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                            <i data-feather="alert-triangle" class="w-6 h-6 text-yellow-600"></i>
                        </div>
                    </div>
                </div>
                
                <div class="glassmorphism rounded-lg p-4 animate-slide-in" style="animation-delay: 0.3s">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-2xl font-bold text-gray-800">89%</p>
                            <p class="text-sm text-gray-600">Auto-fixed</p>
                        </div>
                        <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                            <i data-feather="check-circle" class="w-6 h-6 text-green-600"></i>
                        </div>
                    </div>
                </div>
                
                <div class="glassmorphism rounded-lg p-4 animate-slide-in" style="animation-delay: 0.4s">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-2xl font-bold text-gray-800">3.2M</p>
                            <p class="text-sm text-gray-600">Records Scanned</p>
                        </div>
                        <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                            <i data-feather="database" class="w-6 h-6 text-purple-600"></i>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Main Content Grid -->
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
                <!-- Active Issues -->
                <div class="lg:col-span-2 glassmorphism rounded-xl p-6">
                    <div class="flex items-center justify-between mb-6">
                        <h3 class="text-lg font-semibold text-gray-800">Active Issues</h3>
                        <div class="flex items-center space-x-2">
                            <select class="px-3 py-1.5 bg-white border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-teal-500">
                                <option>All Severities</option>
                                <option>Critical Only</option>
                                <option>Major & Above</option>
                            </select>
                            <button class="p-1.5 rounded-lg hover:bg-gray-100 transition-colors">
                                <i data-feather="filter" class="w-4 h-4 text-gray-600"></i>
                            </button>
                        </div>
                    </div>
                    
                    <div class="space-y-4 max-h-96 overflow-y-auto pr-2">
                        <!-- Critical Issue -->
                        <div class="border border-red-200 rounded-lg p-4 bg-red-50">
                            <div class="flex items-start justify-between mb-3">
                                <div class="flex items-start space-x-3">
                                    <i data-feather="alert-octagon" class="w-5 h-5 text-red-600 mt-0.5"></i>
                                    <div>
                                        <h4 class="font-semibold text-gray-800">Missing Required Fields</h4>
                                        <p class="text-sm text-gray-600 mt-1">245 customer records have null email addresses</p>
                                        <div class="flex items-center space-x-4 mt-2 text-xs">
                                            <span class="severity-critical px-2 py-1 rounded">Critical</span>
                                            <span class="text-gray-500">Source: Shopify</span>
                                            <span class="text-gray-500">Detected: 2 hours ago</span>
                                        </div>
                                    </div>
                                </div>
                                <button class="text-teal-600 text-sm font-medium hover:text-teal-700">Fix Now →</button>
                            </div>
                            <div class="bg-white rounded p-3 text-sm">
                                <p class="text-gray-700 mb-2">Affected tables: <code class="bg-gray-100 px-1 rounded">customers</code>, <code class="bg-gray-100 px-1 rounded">orders</code></p>
                                <div class="flex items-center justify-between">
                                    <span class="text-gray-600">Impact: High - Affects email campaigns</span>
                                    <button class="text-red-600 text-xs font-medium hover:text-red-700">View Details</button>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Major Issue -->
                        <div class="border border-yellow-200 rounded-lg p-4 bg-yellow-50">
                            <div class="flex items-start justify-between mb-3">
                                <div class="flex items-start space-x-3">
                                    <i data-feather="alert-triangle" class="w-5 h-5 text-yellow-600 mt-0.5"></i>
                                    <div>
                                        <h4 class="font-semibold text-gray-800">Data Format Inconsistency</h4>
                                        <p class="text-sm text-gray-600 mt-1">Phone numbers stored in 3 different formats</p>
                                        <div class="flex items-center space-x-4 mt-2 text-xs">
                                            <span class="severity-major px-2 py-1 rounded">Major</span>
                                            <span class="text-gray-500">Source: Multiple</span>
                                            <span class="text-gray-500">Detected: 5 hours ago</span>
                                        </div>
                                    </div>
                                </div>
                                <button class="text-teal-600 text-sm font-medium hover:text-teal-700">Standardize →</button>
                            </div>
                        </div>
                        
                        <!-- Minor Issue -->
                        <div class="border border-blue-200 rounded-lg p-4 bg-blue-50">
                            <div class="flex items-start justify-between mb-3">
                                <div class="flex items-start space-x-3">
                                    <i data-feather="info" class="w-5 h-5 text-blue-600 mt-0.5"></i>
                                    <div>
                                        <h4 class="font-semibold text-gray-800">Duplicate Records Detected</h4>
                                        <p class="text-sm text-gray-600 mt-1">12 potential duplicate customer entries found</p>
                                        <div class="flex items-center space-x-4 mt-2 text-xs">
                                            <span class="severity-minor px-2 py-1 rounded">Minor</span>
                                            <span class="text-gray-500">Source: CRM</span>
                                            <span class="text-gray-500">Detected: Yesterday</span>
                                        </div>
                                    </div>
                                </div>
                                <button class="text-teal-600 text-sm font-medium hover:text-teal-700">Review →</button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mt-4 flex items-center justify-between text-sm">
                        <span class="text-gray-600">Showing 3 of 156 issues</span>
                        <button class="text-teal-600 font-medium hover:text-teal-700">View All Issues →</button>
                    </div>
                </div>

                <!-- Quality Rules -->
                <div class="glassmorphism rounded-xl p-6">
                    <div class="flex items-center justify-between mb-6">
                        <h3 class="text-lg font-semibold text-gray-800">Quality Rules</h3>
                        <button class="text-sm text-teal-600 font-medium hover:text-teal-700">Manage</button>
                    </div>
                    
                    <div class="space-y-3">
                        <div class="border border-gray-200 rounded-lg p-3 hover:border-teal-400 transition-colors cursor-pointer">
                            <div class="flex items-center justify-between mb-2">
                                <div class="flex items-center space-x-3">
                                    <div class="rule-status bg-green-500"></div>
                                    <h4 class="font-medium text-gray-800 text-sm">Email Format Validation</h4>
                                </div>
                                <label class="toggle-switch active" onclick="event.stopPropagation(); toggleSwitch(this)"></label>
                            </div>
                            <p class="text-xs text-gray-600 ml-6">Validates email addresses using RFC 5322</p>
                            <div class="flex items-center justify-between mt-2 text-xs text-gray-500 ml-6">
                                <span>Applied to: 5 sources</span>
                                <span class="text-green-600">Passing</span>
                            </div>
                        </div>
                        
                        <div class="border border-gray-200 rounded-lg p-3 hover:border-teal-400 transition-colors cursor-pointer">
                            <div class="flex items-center justify-between mb-2">
                                <div class="flex items-center space-x-3">
                                    <div class="rule-status bg-red-500"></div>
                                    <h4 class="font-medium text-gray-800 text-sm">Required Fields Check</h4>
                                </div>
                                <label class="toggle-switch active" onclick="event.stopPropagation(); toggleSwitch(this)"></label>
                            </div>
                            <p class="text-xs text-gray-600 ml-6">Ensures critical fields are not null</p>
                            <div class="flex items-center justify-between mt-2 text-xs text-gray-500 ml-6">
                                <span>Applied to: All sources</span>
                                <span class="text-red-600">245 violations</span>
                            </div>
                        </div>
                        
                        <div class="border border-gray-200 rounded-lg p-3 hover:border-teal-400 transition-colors cursor-pointer">
                            <div class="flex items-center justify-between mb-2">
                                <div class="flex items-center space-x-3">
                                    <div class="rule-status bg-yellow-500"></div>
                                    <h4 class="font-medium text-gray-800 text-sm">Date Range Validation</h4>
                                </div>
                                <label class="toggle-switch active" onclick="event.stopPropagation(); toggleSwitch(this)"></label>
                            </div>
                            <p class="text-xs text-gray-600 ml-6">Checks dates are within valid ranges</p>
                            <div class="flex items-center justify-between mt-2 text-xs text-gray-500 ml-6">
                                <span>Applied to: 3 sources</span>
                                <span class="text-yellow-600">12 warnings</span>
                            </div>
                        </div>
                        
                        <div class="border border-gray-200 rounded-lg p-3 hover:border-teal-400 transition-colors cursor-pointer">
                            <div class="flex items-center justify-between mb-2">
                                <div class="flex items-center space-x-3">
                                    <div class="rule-status bg-green-500"></div>
                                    <h4 class="font-medium text-gray-800 text-sm">Duplicate Detection</h4>
                                </div>
                                <label class="toggle-switch active" onclick="event.stopPropagation(); toggleSwitch(this)"></label>
                            </div>
                            <p class="text-xs text-gray-600 ml-6">Identifies potential duplicate records</p>
                            <div class="flex items-center justify-between mt-2 text-xs text-gray-500 ml-6">
                                <span>Applied to: 4 sources</span>
                                <span class="text-green-600">Passing</span>
                            </div>
                        </div>
                    </div>
                    
                    <button class="w-full mt-4 rule-builder" onclick="openRuleBuilder()">
                        <i data-feather="plus-circle" class="w-6 h-6 text-gray-400 mx-auto mb-2"></i>
                        <p class="text-sm text-gray-600">Add New Rule</p>
                    </button>
                </div>
            </div>

            <!-- Quality Trends & Heatmap -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
                <!-- Quality Trend Chart -->
                <div class="glassmorphism rounded-xl p-6">
                    <div class="flex items-center justify-between mb-6">
                        <h3 class="text-lg font-semibold text-gray-800">Quality Score Trend</h3>
                        <select class="px-3 py-1.5 bg-white border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-teal-500">
                            <option>Last 30 Days</option>
                            <option>Last 90 Days</option>
                            <option>Last Year</option>
                        </select>
                    </div>
                    <canvas id="qualityTrendChart" height="300"></canvas>
                </div>
                
                <!-- Source Quality Heatmap -->
                <div class="glassmorphism rounded-xl p-6">
                    <h3 class="text-lg font-semibold text-gray-800 mb-6">Quality by Source & Dimension</h3>
                    <div class="overflow-x-auto">
                        <table class="w-full">
                            <thead>
                                <tr>
                                    <th class="text-left text-sm font-medium text-gray-600 pb-3">Source</th>
                                    <th class="text-center text-sm font-medium text-gray-600 pb-3">Complete</th>
                                    <th class="text-center text-sm font-medium text-gray-600 pb-3">Accurate</th>
                                    <th class="text-center text-sm font-medium text-gray-600 pb-3">Consistent</th>
                                    <th class="text-center text-sm font-medium text-gray-600 pb-3">Timely</th>
                                    <th class="text-center text-sm font-medium text-gray-600 pb-3">Overall</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="py-2 text-sm font-medium text-gray-800">Shopify</td>
                                    <td class="py-2 px-2">
                                        <div class="heatmap-cell heatmap-excellent rounded text-white text-xs font-bold py-1 text-center">98</div>
                                    </td>
                                    <td class="py-2 px-2">
                                        <div class="heatmap-cell heatmap-excellent rounded text-white text-xs font-bold py-1 text-center">96</div>
                                    </td>
                                    <td class="py-2 px-2">
                                        <div class="heatmap-cell heatmap-good rounded text-white text-xs font-bold py-1 text-center">88</div>
                                    </td>
                                    <td class="py-2 px-2">
                                        <div class="heatmap-cell heatmap-excellent rounded text-white text-xs font-bold py-1 text-center">100</div>
                                    </td>
                                    <td class="py-2 px-2">
                                        <div class="heatmap-cell heatmap-excellent rounded text-white text-xs font-bold py-1 text-center">96</div>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="py-2 text-sm font-medium text-gray-800">Google Analytics</td>
                                    <td class="py-2 px-2">
                                        <div class="heatmap-cell heatmap-excellent rounded text-white text-xs font-bold py-1 text-center">100</div>
                                    </td>
                                    <td class="py-2 px-2">
                                        <div class="heatmap-cell heatmap-excellent rounded text-white text-xs font-bold py-1 text-center">94</div>
                                    </td>
                                    <td class="py-2 px-2">
                                        <div class="heatmap-cell heatmap-excellent rounded text-white text-xs font-bold py-1 text-center">92</div>
                                    </td>
                                    <td class="py-2 px-2">
                                        <div class="heatmap-cell heatmap-good rounded text-white text-xs font-bold py-1 text-center">85</div>
                                    </td>
                                    <td class="py-2 px-2">
                                        <div class="heatmap-cell heatmap-good rounded text-white text-xs font-bold py-1 text-center">93</div>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="py-2 text-sm font-medium text-gray-800">Stripe</td>
                                    <td class="py-2 px-2">
                                        <div class="heatmap-cell heatmap-excellent rounded text-white text-xs font-bold py-1 text-center">95</div>
                                    </td>
                                    <td class="py-2 px-2">
                                        <div class="heatmap-cell heatmap-excellent rounded text-white text-xs font-bold py-1 text-center">99</div>
                                    </td>
                                    <td class="py-2 px-2">
                                        <div class="heatmap-cell heatmap-excellent rounded text-white text-xs font-bold py-1 text-center">97</div>
                                    </td>
                                    <td class="py-2 px-2">
                                        <div class="heatmap-cell heatmap-excellent rounded text-white text-xs font-bold py-1 text-center">98</div>
                                    </td>
                                    <td class="py-2 px-2">
                                        <div class="heatmap-cell heatmap-excellent rounded text-white text-xs font-bold py-1 text-center">97</div>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="py-2 text-sm font-medium text-gray-800">PostgreSQL</td>
                                    <td class="py-2 px-2">
                                        <div class="heatmap-cell heatmap-good rounded text-white text-xs font-bold py-1 text-center">92</div>
                                    </td>
                                    <td class="py-2 px-2">
                                        <div class="heatmap-cell heatmap-good rounded text-white text-xs font-bold py-1 text-center">90</div>
                                    </td>
                                    <td class="py-2 px-2">
                                        <div class="heatmap-cell heatmap-fair rounded text-white text-xs font-bold py-1 text-center">78</div>
                                    </td>
                                    <td class="py-2 px-2">
                                        <div class="heatmap-cell heatmap-excellent rounded text-white text-xs font-bold py-1 text-center">95</div>
                                    </td>
                                    <td class="py-2 px-2">
                                        <div class="heatmap-cell heatmap-good rounded text-white text-xs font-bold py-1 text-center">89</div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="flex items-center justify-center space-x-4 mt-4 text-xs">
                        <div class="flex items-center space-x-2">
                            <div class="w-4 h-4 heatmap-excellent rounded"></div>
                            <span class="text-gray-600">Excellent (95-100)</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <div class="w-4 h-4 heatmap-good rounded"></div>
                            <span class="text-gray-600">Good (85-94)</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <div class="w-4 h-4 heatmap-fair rounded"></div>
                            <span class="text-gray-600">Fair (70-84)</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <div class="w-4 h-4 heatmap-poor rounded"></div>
                            <span class="text-gray-600">Poor (<70)</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- AI Recommendations -->
            <div class="glassmorphism rounded-xl p-6">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-lg font-semibold text-gray-800 flex items-center space-x-2">
                        <i data-feather="cpu" class="w-5 h-5 text-purple-600"></i>
                        <span>AI-Powered Recommendations</span>
                    </h3>
                    <button class="text-sm text-purple-600 font-medium hover:text-purple-700">Generate New</button>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <div class="bg-gradient-to-br from-purple-50 to-white rounded-lg p-4 border border-purple-100">
                        <div class="flex items-start space-x-3">
                            <div class="w-10 h-10 bg-purple-200 rounded-lg flex items-center justify-center flex-shrink-0">
                                <i data-feather="zap" class="w-5 h-5 text-purple-700"></i>
                            </div>
                            <div>
                                <h4 class="font-medium text-gray-800">Optimize Email Validation</h4>
                                <p class="text-sm text-gray-600 mt-1">Implement stricter email validation to prevent 87% of current email-related issues.</p>
                                <button class="text-sm text-purple-600 font-medium mt-3 hover:text-purple-700">Apply Rule →</button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-gradient-to-br from-blue-50 to-white rounded-lg p-4 border border-blue-100">
                        <div class="flex items-start space-x-3">
                            <div class="w-10 h-10 bg-blue-200 rounded-lg flex items-center justify-center flex-shrink-0">
                                <i data-feather="shield" class="w-5 h-5 text-blue-700"></i>
                            </div>
                            <div>
                                <h4 class="font-medium text-gray-800">Add Phone Standardization</h4>
                                <p class="text-sm text-gray-600 mt-1">Standardize phone formats across all sources to improve consistency by 15%.</p>
                                <button class="text-sm text-blue-600 font-medium mt-3 hover:text-blue-700">Configure →</button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-gradient-to-br from-green-50 to-white rounded-lg p-4 border border-green-100">
                        <div class="flex items-start space-x-3">
                            <div class="w-10 h-10 bg-green-200 rounded-lg flex items-center justify-center flex-shrink-0">
                                <i data-feather="trending-up" class="w-5 h-5 text-green-700"></i>
                            </div>
                            <div>
                                <h4 class="font-medium text-gray-800">Schedule Off-peak Checks</h4>
                                <p class="text-sm text-gray-600 mt-1">Run intensive quality checks during off-peak hours to reduce system load by 40%.</p>
                                <button class="text-sm text-green-600 font-medium mt-3 hover:text-green-700">Schedule →</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script>
        // Initialize Feather Icons
        feather.replace();

        // Toggle Switch
        function toggleSwitch(element) {
            element.classList.toggle('active');
        }

        // Open Rule Builder Modal
        function openRuleBuilder() {
            console.log('Opening rule builder...');
        }

        // Quality Trend Chart
        function initQualityTrendChart() {
            const ctx = document.getElementById('qualityTrendChart').getContext('2d');
            new Chart(ctx, {
                type: 'line',
                data: {
                    labels: Array.from({length: 30}, (_, i) => {
                        const date = new Date();
                        date.setDate(date.getDate() - (29 - i));
                        return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
                    }),
                    datasets: [{
                        label: 'Overall Score',
                        data: [88, 89, 87, 89, 90, 89, 91, 92, 91, 90, 91, 92, 91, 93, 92, 91, 92, 93, 92, 93, 94, 93, 92, 93, 94, 93, 94, 93, 94, 94],
                        borderColor: '#14B8A6',
                        backgroundColor: 'rgba(20, 184, 166, 0.1)',
                        borderWidth: 2,
                        tension: 0.4,
                        fill: true
                    }, {
                        label: 'Target',
                        data: Array(30).fill(95),
                        borderColor: '#9CA3AF',
                        borderWidth: 2,
                        borderDash: [5, 5],
                        tension: 0,
                        fill: false,
                        pointRadius: 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    interaction: {
                        intersect: false,
                        mode: 'index'
                    },
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                usePointStyle: true,
                                padding: 15
                            }
                        },
                        tooltip: {
                            backgroundColor: 'rgba(255, 255, 255, 0.9)',
                            titleColor: '#1F2937',
                            bodyColor: '#4B5563',
                            borderColor: '#E5E7EB',
                            borderWidth: 1,
                            padding: 12,
                            displayColors: true,
                            callbacks: {
                                label: function(context) {
                                    return context.dataset.label + ': ' + context.parsed.y + '%';
                                }
                            }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: false,
                            min: 80,
                            max: 100,
                            ticks: {
                                callback: function(value) {
                                    return value + '%';
                                }
                            },
                            grid: {
                                color: 'rgba(0, 0, 0, 0.05)'
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            }
                        }
                    }
                }
            });
        }

        // Initialize chart
        initQualityTrendChart();

        // Simulate real-time updates
        function updateQualityScores() {
            // Update progress bars
            const progressBars = document.querySelectorAll('.progress-fill');
            progressBars.forEach(bar => {
                const currentWidth = parseInt(bar.style.width);
                const change = (Math.random() - 0.5) * 2;
                const newWidth = Math.max(0, Math.min(100, currentWidth + change));
                bar.style.width = newWidth + '%';
            });

            // Update issue counts
            const issueCounts = document.querySelectorAll('[data-issue-count]');
            issueCounts.forEach(count => {
                const current = parseInt(count.textContent);
                const change = Math.floor(Math.random() * 3) - 1;
                count.textContent = Math.max(0, current + change);
            });
        }

        // Update every 10 seconds
        setInterval(updateQualityScores, 10000);
    </script>
</body>
</html>