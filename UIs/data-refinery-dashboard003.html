<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>DataFlow Dashboard - Premium Data Intelligence</title>
  <style>
    /* Design System - Premium Variables */
    :root {
      /* Primary Colors - Teal Palette */
      --primary-50: #f0fdfa;
      --primary-100: #ccfbf1;
      --primary-200: #99f6e4;
      --primary-300: #5eead4;
      --primary-400: #2dd4bf;
      --primary-500: #14b8a6;
      --primary-600: #0d9488;
      --primary-700: #0f766e;
      --primary-800: #115e59;
      --primary-900: #134e4a;
      
      /* Neutral Background */
      --bg-primary: #EAE4D5;
      --bg-secondary: #F5F1E8;
      --bg-tertiary: #FFFFFF;
      --bg-dark: #2C3E3B;
      
      /* Text Colors */
      --text-primary: #1F2937;
      --text-secondary: #4B5563;
      --text-tertiary: #6B7280;
      --text-inverse: #FFFFFF;
      
      /* Accent Colors */
      --accent-success: #10B981;
      --accent-warning: #F59E0B;
      --accent-error: #EF4444;
      --accent-info: #3B82F6;
      
      /* Shadows */
      --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
      --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
      --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
      --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
      
      /* Animations */
      --transition-base: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      --transition-fast: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
    }
    
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
      background-color: var(--bg-primary);
      color: var(--text-primary);
      line-height: 1.6;
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
    }
    
    /* Dashboard Layout */
    .dashboard-layout {
      display: flex;
      min-height: 100vh;
    }
    
    /* Sidebar */
    .sidebar {
      width: 280px;
      background: var(--bg-tertiary);
      border-right: 1px solid rgba(0, 0, 0, 0.05);
      display: flex;
      flex-direction: column;
      position: fixed;
      height: 100vh;
      z-index: 100;
    }
    
    .sidebar-header {
      padding: 1.5rem;
      border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    }
    
    .sidebar-logo {
      display: flex;
      align-items: center;
      gap: 0.75rem;
      text-decoration: none;
    }
    
    .sidebar-logo-icon {
      width: 40px;
      height: 40px;
      background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
      border-radius: 10px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-weight: bold;
      font-size: 1.25rem;
    }
    
    .sidebar-logo-text {
      font-size: 1.25rem;
      font-weight: 700;
      color: var(--text-primary);
    }
    
    .sidebar-nav {
      flex: 1;
      overflow-y: auto;
      padding: 1rem 0;
    }
    
    .nav-section {
      padding: 0 1rem;
      margin-bottom: 1.5rem;
    }
    
    .nav-section-title {
      font-size: 0.75rem;
      font-weight: 600;
      color: var(--text-tertiary);
      text-transform: uppercase;
      letter-spacing: 0.05em;
      margin-bottom: 0.5rem;
      padding: 0 0.75rem;
    }
    
    .nav-item {
      display: flex;
      align-items: center;
      gap: 0.75rem;
      padding: 0.625rem 0.75rem;
      border-radius: 8px;
      color: var(--text-secondary);
      text-decoration: none;
      font-size: 0.9375rem;
      font-weight: 500;
      transition: var(--transition-fast);
      margin-bottom: 0.25rem;
      position: relative;
    }
    
    .nav-item:hover {
      background: var(--bg-secondary);
      color: var(--primary-600);
    }
    
    .nav-item.active {
      background: linear-gradient(135deg, rgba(20, 184, 166, 0.1), rgba(20, 184, 166, 0.05));
      color: var(--primary-600);
      border-left: 3px solid var(--primary-500);
      padding-left: calc(0.75rem - 3px);
    }
    
    .nav-icon {
      width: 20px;
      height: 20px;
      flex-shrink: 0;
    }
    
    .nav-badge {
      margin-left: auto;
      font-size: 0.75rem;
      padding: 0.125rem 0.5rem;
      background: var(--primary-500);
      color: white;
      border-radius: 50px;
      font-weight: 600;
    }
    
    .sidebar-footer {
      padding: 1rem;
      border-top: 1px solid rgba(0, 0, 0, 0.05);
    }
    
    .user-profile {
      display: flex;
      align-items: center;
      gap: 0.75rem;
      padding: 0.75rem;
      background: var(--bg-secondary);
      border-radius: 10px;
      cursor: pointer;
      transition: var(--transition-fast);
    }
    
    .user-profile:hover {
      background: var(--bg-primary);
    }
    
    .user-avatar {
      width: 40px;
      height: 40px;
      background: linear-gradient(135deg, var(--primary-400), var(--primary-600));
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-weight: 600;
    }
    
    .user-info {
      flex: 1;
    }
    
    .user-name {
      font-size: 0.875rem;
      font-weight: 600;
      color: var(--text-primary);
    }
    
    .user-role {
      font-size: 0.75rem;
      color: var(--text-tertiary);
    }
    
    /* Main Content */
    .main-content {
      flex: 1;
      margin-left: 280px;
      background: var(--bg-primary);
    }
    
    /* Top Header */
    .top-header {
      background: var(--bg-tertiary);
      border-bottom: 1px solid rgba(0, 0, 0, 0.05);
      padding: 1.25rem 2rem;
      position: sticky;
      top: 0;
      z-index: 50;
    }
    
    .header-content {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
    
    .header-left {
      display: flex;
      align-items: center;
      gap: 2rem;
    }
    
    .search-bar {
      display: flex;
      align-items: center;
      background: var(--bg-secondary);
      border-radius: 10px;
      padding: 0.625rem 1rem;
      width: 400px;
      transition: var(--transition-fast);
    }
    
    .search-bar:focus-within {
      box-shadow: 0 0 0 3px rgba(20, 184, 166, 0.1);
      border: 1px solid var(--primary-500);
      margin: -1px;
    }
    
    .search-input {
      flex: 1;
      border: none;
      background: transparent;
      outline: none;
      color: var(--text-primary);
      font-size: 0.875rem;
      margin-left: 0.5rem;
    }
    
    .header-right {
      display: flex;
      align-items: center;
      gap: 1rem;
    }
    
    .header-btn {
      width: 40px;
      height: 40px;
      border-radius: 10px;
      border: 1px solid rgba(0, 0, 0, 0.08);
      background: var(--bg-tertiary);
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: var(--transition-fast);
      position: relative;
    }
    
    .header-btn:hover {
      background: var(--bg-secondary);
      border-color: var(--primary-300);
    }
    
    .notification-dot {
      position: absolute;
      top: 8px;
      right: 8px;
      width: 8px;
      height: 8px;
      background: var(--accent-error);
      border-radius: 50%;
      border: 2px solid var(--bg-tertiary);
    }
    
    /* Page Content */
    .page-content {
      padding: 2rem;
    }
    
    .page-header {
      margin-bottom: 2rem;
    }
    
    .page-title {
      font-size: 2rem;
      font-weight: 700;
      color: var(--text-primary);
      margin-bottom: 0.5rem;
    }
    
    .page-subtitle {
      font-size: 1rem;
      color: var(--text-secondary);
    }
    
    /* Dashboard Widgets */
    .dashboard-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 1.5rem;
      margin-bottom: 2rem;
    }
    
    .widget {
      background: var(--bg-tertiary);
      border-radius: 16px;
      padding: 1.5rem;
      border: 1px solid rgba(0, 0, 0, 0.05);
      transition: var(--transition-base);
    }
    
    .widget:hover {
      box-shadow: var(--shadow-lg);
      transform: translateY(-2px);
    }
    
    .widget-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 1.5rem;
    }
    
    .widget-title {
      font-size: 1rem;
      font-weight: 600;
      color: var(--text-primary);
    }
    
    .widget-icon {
      width: 48px;
      height: 48px;
      background: linear-gradient(135deg, rgba(20, 184, 166, 0.1), rgba(20, 184, 166, 0.05));
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: var(--primary-600);
    }
    
    .widget-value {
      font-size: 2.25rem;
      font-weight: 700;
      color: var(--text-primary);
      margin-bottom: 0.5rem;
    }
    
    .widget-label {
      font-size: 0.875rem;
      color: var(--text-tertiary);
    }
    
    .widget-trend {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      margin-top: 1rem;
      padding-top: 1rem;
      border-top: 1px solid var(--bg-secondary);
    }
    
    .trend-value {
      font-size: 0.875rem;
      font-weight: 600;
      display: flex;
      align-items: center;
      gap: 0.25rem;
    }
    
    .trend-positive {
      color: var(--accent-success);
    }
    
    .trend-negative {
      color: var(--accent-error);
    }
    
    /* Chart Widgets */
    .chart-widget {
      grid-column: span 2;
      background: var(--bg-tertiary);
      border-radius: 16px;
      padding: 1.5rem;
      border: 1px solid rgba(0, 0, 0, 0.05);
    }
    
    .chart-controls {
      display: flex;
      gap: 0.5rem;
    }
    
    .chart-btn {
      padding: 0.375rem 0.75rem;
      background: transparent;
      border: 1px solid rgba(0, 0, 0, 0.08);
      border-radius: 6px;
      font-size: 0.8125rem;
      font-weight: 500;
      color: var(--text-secondary);
      cursor: pointer;
      transition: var(--transition-fast);
    }
    
    .chart-btn:hover {
      background: var(--bg-secondary);
      border-color: var(--primary-300);
      color: var(--primary-600);
    }
    
    .chart-btn.active {
      background: var(--primary-500);
      border-color: var(--primary-500);
      color: white;
    }
    
    .chart-container {
      height: 300px;
      margin-top: 1.5rem;
      background: var(--bg-secondary);
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
      overflow: hidden;
    }
    
    /* Activity Feed */
    .activity-widget {
      background: var(--bg-tertiary);
      border-radius: 16px;
      padding: 1.5rem;
      border: 1px solid rgba(0, 0, 0, 0.05);
    }
    
    .activity-list {
      margin-top: 1.5rem;
    }
    
    .activity-item {
      display: flex;
      gap: 1rem;
      padding: 1rem 0;
      border-bottom: 1px solid var(--bg-secondary);
    }
    
    .activity-item:last-child {
      border-bottom: none;
      padding-bottom: 0;
    }
    
    .activity-icon {
      width: 40px;
      height: 40px;
      background: var(--bg-secondary);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;
    }
    
    .activity-content {
      flex: 1;
    }
    
    .activity-title {
      font-size: 0.875rem;
      font-weight: 500;
      color: var(--text-primary);
      margin-bottom: 0.25rem;
    }
    
    .activity-description {
      font-size: 0.8125rem;
      color: var(--text-tertiary);
      margin-bottom: 0.25rem;
    }
    
    .activity-time {
      font-size: 0.75rem;
      color: var(--text-tertiary);
    }
    
    /* Data Sources Grid */
    .data-sources-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
      gap: 1rem;
    }
    
    .data-source-card {
      background: var(--bg-tertiary);
      border-radius: 12px;
      padding: 1.25rem;
      border: 1px solid rgba(0, 0, 0, 0.05);
      cursor: pointer;
      transition: var(--transition-base);
    }
    
    .data-source-card:hover {
      box-shadow: var(--shadow-md);
      transform: translateY(-1px);
      border-color: var(--primary-300);
    }
    
    .source-header {
      display: flex;
      align-items: center;
      gap: 1rem;
      margin-bottom: 1rem;
    }
    
    .source-icon {
      width: 48px;
      height: 48px;
      background: var(--bg-secondary);
      border-radius: 10px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 600;
      color: var(--primary-600);
    }
    
    .source-info {
      flex: 1;
    }
    
    .source-name {
      font-size: 1rem;
      font-weight: 600;
      color: var(--text-primary);
      margin-bottom: 0.25rem;
    }
    
    .source-type {
      font-size: 0.8125rem;
      color: var(--text-tertiary);
    }
    
    .source-status {
      display: inline-flex;
      align-items: center;
      gap: 0.375rem;
      padding: 0.25rem 0.75rem;
      background: rgba(16, 185, 129, 0.1);
      color: var(--accent-success);
      border-radius: 50px;
      font-size: 0.75rem;
      font-weight: 600;
      margin-top: 1rem;
    }
    
    .status-dot {
      width: 6px;
      height: 6px;
      background: currentColor;
      border-radius: 50%;
    }
    
    /* Floating Action Button */
    .fab {
      position: fixed;
      bottom: 2rem;
      right: 2rem;
      width: 56px;
      height: 56px;
      background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      box-shadow: var(--shadow-lg);
      cursor: pointer;
      transition: var(--transition-base);
    }
    
    .fab:hover {
      transform: scale(1.1);
      box-shadow: var(--shadow-xl);
    }
    
    /* AI Assistant Panel */
    .ai-panel {
      position: fixed;
      right: -400px;
      top: 0;
      width: 400px;
      height: 100vh;
      background: var(--bg-tertiary);
      box-shadow: var(--shadow-xl);
      z-index: 1000;
      transition: right 0.3s ease;
      display: flex;
      flex-direction: column;
    }
    
    .ai-panel.open {
      right: 0;
    }
    
    .ai-header {
      padding: 1.5rem;
      border-bottom: 1px solid rgba(0, 0, 0, 0.05);
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
    
    .ai-title {
      font-size: 1.125rem;
      font-weight: 600;
      color: var(--text-primary);
    }
    
    .ai-close {
      width: 32px;
      height: 32px;
      border-radius: 6px;
      background: var(--bg-secondary);
      border: none;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: var(--transition-fast);
    }
    
    .ai-close:hover {
      background: var(--bg-primary);
    }
    
    .ai-content {
      flex: 1;
      overflow-y: auto;
      padding: 1.5rem;
    }
    
    .ai-message {
      margin-bottom: 1rem;
    }
    
    .ai-message-user {
      text-align: right;
    }
    
    .ai-bubble {
      display: inline-block;
      padding: 0.75rem 1rem;
      border-radius: 12px;
      max-width: 80%;
      font-size: 0.875rem;
      line-height: 1.5;
    }
    
    .ai-bubble-assistant {
      background: var(--bg-secondary);
      color: var(--text-primary);
    }
    
    .ai-bubble-user {
      background: var(--primary-500);
      color: white;
    }
    
    .ai-input-area {
      padding: 1.5rem;
      border-top: 1px solid rgba(0, 0, 0, 0.05);
    }
    
    .ai-input-wrapper {
      display: flex;
      gap: 0.75rem;
    }
    
    .ai-input {
      flex: 1;
      padding: 0.75rem 1rem;
      background: var(--bg-secondary);
      border: 1px solid rgba(0, 0, 0, 0.08);
      border-radius: 10px;
      outline: none;
      font-size: 0.875rem;
      transition: var(--transition-fast);
    }
    
    .ai-input:focus {
      border-color: var(--primary-500);
      box-shadow: 0 0 0 3px rgba(20, 184, 166, 0.1);
    }
    
    .ai-send {
      width: 44px;
      height: 44px;
      background: var(--primary-500);
      border: none;
      border-radius: 10px;
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: var(--transition-fast);
    }
    
    .ai-send:hover {
      background: var(--primary-600);
    }
    
    /* Responsive */
    @media (max-width: 1024px) {
      .dashboard-grid {
        grid-template-columns: 1fr;
      }
      
      .chart-widget {
        grid-column: span 1;
      }
    }
    
    @media (max-width: 768px) {
      .sidebar {
        transform: translateX(-100%);
      }
      
      .main-content {
        margin-left: 0;
      }
      
      .search-bar {
        width: 200px;
      }
      
      .ai-panel {
        width: 100%;
        right: -100%;
      }
    }
  </style>
</head>
<body>
  <div class="dashboard-layout">
    <!-- Sidebar -->
    <aside class="sidebar">
      <div class="sidebar-header">
        <a href="#" class="sidebar-logo">
          <div class="sidebar-logo-icon">DF</div>
          <span class="sidebar-logo-text">DataFlow</span>
        </a>
      </div>
      
      <nav class="sidebar-nav">
        <div class="nav-section">
          <div class="nav-section-title">Main</div>
          <a href="#" class="nav-item active">
            <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <rect x="3" y="3" width="7" height="7" rx="1"/>
              <rect x="14" y="3" width="7" height="7" rx="1"/>
              <rect x="3" y="14" width="7" height="7" rx="1"/>
              <rect x="14" y="14" width="7" height="7" rx="1"/>
            </svg>
            <span>Dashboard</span>
          </a>
          <a href="#" class="nav-item">
            <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
              <circle cx="12" cy="7" r="4"/>
            </svg>
            <span>Data Sources</span>
            <span class="nav-badge">12</span>
          </a>
          <a href="#" class="nav-item">
            <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M3 3v18h18"/>
              <path d="M18 17V9"/>
              <path d="M13 17V5"/>
              <path d="M8 17v-3"/>
            </svg>
            <span>Analytics</span>
          </a>
          <a href="#" class="nav-item">
            <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"/>
              <polyline points="3.27 6.96 12 12.01 20.73 6.96"/>
              <line x1="12" y1="22.08" x2="12" y2="12"/>
            </svg>
            <span>Pipelines</span>
          </a>
        </div>
        
        <div class="nav-section">
          <div class="nav-section-title">Intelligence</div>
          <a href="#" class="nav-item">
            <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M12 2L2 7l10 5 10-5-10-5z"/>
              <path d="M2 17l10 5 10-5"/>
              <path d="M2 12l10 5 10-5"/>
            </svg>
            <span>AI Insights</span>
          </a>
          <a href="#" class="nav-item">
            <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
              <line x1="9" y1="9" x2="15" y2="15"/>
              <line x1="15" y1="9" x2="9" y2="15"/>
            </svg>
            <span>Data Quality</span>
          </a>
          <a href="#" class="nav-item">
            <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
              <polyline points="14 2 14 8 20 8"/>
              <line x1="16" y1="13" x2="8" y2="13"/>
              <line x1="16" y1="17" x2="8" y2="17"/>
            </svg>
            <span>Reports</span>
          </a>
        </div>
        
        <div class="nav-section">
          <div class="nav-section-title">Settings</div>
          <a href="#" class="nav-item">
            <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <circle cx="12" cy="12" r="3"/>
              <path d="M12 1v6m0 6v6m11-6h-6m-6 0H1m20.485-6.485l-4.243 4.243m-8.484 0l-4.243-4.243m0 8.484l4.243-4.243m8.484 0l4.243 4.243"/>
            </svg>
            <span>Configuration</span>
          </a>
          <a href="#" class="nav-item">
            <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"/>
              <circle cx="9" cy="7" r="4"/>
              <path d="M23 21v-2a4 4 0 0 0-3-3.87"/>
              <path d="M16 3.13a4 4 0 0 1 0 7.75"/>
            </svg>
            <span>Team</span>
          </a>
        </div>
      </nav>
      
      <div class="sidebar-footer">
        <div class="user-profile">
          <div class="user-avatar">JD</div>
          <div class="user-info">
            <div class="user-name">John Doe</div>
            <div class="user-role">Administrator</div>
          </div>
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <polyline points="6 9 12 15 18 9"></polyline>
          </svg>
        </div>
      </div>
    </aside>
    
    <!-- Main Content -->
    <main class="main-content">
      <!-- Top Header -->
      <header class="top-header">
        <div class="header-content">
          <div class="header-left">
            <div class="search-bar">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <circle cx="11" cy="11" r="8"/>
                <path d="m21 21-4.35-4.35"/>
              </svg>
              <input type="text" class="search-input" placeholder="Search data, sources, reports...">
            </div>
          </div>
          
          <div class="header-right">
            <button class="header-btn">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9"/>
                <path d="M13.73 21a2 2 0 0 1-3.46 0"/>
              </svg>
              <span class="notification-dot"></span>
            </button>
            
            <button class="header-btn">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <circle cx="12" cy="12" r="1"/>
                <circle cx="12" cy="5" r="1"/>
                <circle cx="12" cy="19" r="1"/>
              </svg>
            </button>
          </div>
        </div>
      </header>
      
      <!-- Page Content -->
      <div class="page-content">
        <div class="page-header">
          <h1 class="page-title">Welcome back, John!</h1>
          <p class="page-subtitle">Here's what's happening with your data today</p>
        </div>
        
        <!-- Dashboard Widgets -->
        <div class="dashboard-grid">
          <div class="widget">
            <div class="widget-header">
              <div>
                <div class="widget-value">2.4M</div>
                <div class="widget-label">Records Processed</div>
              </div>
              <div class="widget-icon">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M3 3v18h18"/>
                  <path d="M18 17V9"/>
                  <path d="M13 17V5"/>
                  <path d="M8 17v-3"/>
                </svg>
              </div>
            </div>
            <div class="widget-trend">
              <span class="trend-value trend-positive">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <polyline points="23 6 13.5 15.5 8.5 10.5 1 18"></polyline>
                  <polyline points="17 6 23 6 23 12"></polyline>
                </svg>
                12.5%
              </span>
              <span style="color: var(--text-tertiary); font-size: 0.875rem;">vs last week</span>
            </div>
          </div>
          
          <div class="widget">
            <div class="widget-header">
              <div>
                <div class="widget-value">98.5%</div>
                <div class="widget-label">Data Quality Score</div>
              </div>
              <div class="widget-icon">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M12 2L2 7l10 5 10-5-10-5z"/>
                  <path d="M2 17l10 5 10-5"/>
                  <path d="M2 12l10 5 10-5"/>
                </svg>
              </div>
            </div>
            <div class="widget-trend">
              <span class="trend-value trend-negative">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <polyline points="23 18 13.5 8.5 8.5 13.5 1 6"></polyline>
                  <polyline points="17 18 23 18 23 12"></polyline>
                </svg>
                2.1%
              </span>
              <span style="color: var(--text-tertiary); font-size: 0.875rem;">vs last week</span>
            </div>
          </div>
          
          <div class="widget">
            <div class="widget-header">
              <div>
                <div class="widget-value">145</div>
                <div class="widget-label">Active Sources</div>
              </div>
              <div class="widget-icon">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
                  <circle cx="12" cy="7" r="4"/>
                </svg>
              </div>
            </div>
            <div class="widget-trend">
              <span class="trend-value trend-positive">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <polyline points="23 6 13.5 15.5 8.5 10.5 1 18"></polyline>
                  <polyline points="17 6 23 6 23 12"></polyline>
                </svg>
                8.3%
              </span>
              <span style="color: var(--text-tertiary); font-size: 0.875rem;">vs last week</span>
            </div>
          </div>
        </div>
        
        <!-- Chart Section -->
        <div class="chart-widget">
          <div class="widget-header">
            <h3 class="widget-title">Data Processing Trends</h3>
            <div class="chart-controls">
              <button class="chart-btn">Day</button>
              <button class="chart-btn active">Week</button>
              <button class="chart-btn">Month</button>
              <button class="chart-btn">Year</button>
            </div>
          </div>
          <div class="chart-container">
            <svg width="100%" height="100%" viewBox="0 0 800 300" style="opacity: 0.1;">
              <path d="M50 250 L150 200 L250 180 L350 120 L450 140 L550 80 L650 100 L750 60" 
                    fill="none" 
                    stroke="var(--primary-500)" 
                    stroke-width="3"/>
              <path d="M50 250 L150 200 L250 180 L350 120 L450 140 L550 80 L650 100 L750 60 L750 300 L50 300" 
                    fill="url(#gradient)" 
                    opacity="0.2"/>
              <defs>
                <linearGradient id="gradient" x1="0%" y1="0%" x2="0%" y2="100%">
                  <stop offset="0%" style="stop-color:var(--primary-500);stop-opacity:0.5" />
                  <stop offset="100%" style="stop-color:var(--primary-500);stop-opacity:0" />
                </linearGradient>
              </defs>
            </svg>
          </div>
        </div>
        
        <!-- Activity and Data Sources -->
        <div class="dashboard-grid" style="margin-top: 1.5rem;">
          <div class="activity-widget">
            <div class="widget-header">
              <h3 class="widget-title">Recent Activity</h3>
              <a href="#" style="font-size: 0.875rem; color: var(--primary-600); text-decoration: none;">View all</a>
            </div>
            <div class="activity-list">
              <div class="activity-item">
                <div class="activity-icon" style="background: rgba(16, 185, 129, 0.1); color: var(--accent-success);">
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <polyline points="20 6 9 17 4 12"></polyline>
                  </svg>
                </div>
                <div class="activity-content">
                  <div class="activity-title">Data sync completed</div>
                  <div class="activity-description">Shopify integration synced 12,543 records</div>
                  <div class="activity-time">5 minutes ago</div>
                </div>
              </div>
              
              <div class="activity-item">
                <div class="activity-icon" style="background: rgba(59, 130, 246, 0.1); color: var(