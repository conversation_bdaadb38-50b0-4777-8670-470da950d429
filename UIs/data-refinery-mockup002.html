<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>DataFlow - Premium Data Intelligence Platform</title>
  <style>
    /* Design System - Premium Variables */
    :root {
      /* Primary Colors - Teal Palette */
      --primary-50: #f0fdfa;
      --primary-100: #ccfbf1;
      --primary-200: #99f6e4;
      --primary-300: #5eead4;
      --primary-400: #2dd4bf;
      --primary-500: #14b8a6;
      --primary-600: #0d9488;
      --primary-700: #0f766e;
      --primary-800: #115e59;
      --primary-900: #134e4a;
      
      /* Neutral Background */
      --bg-primary: #EAE4D5;
      --bg-secondary: #F5F1E8;
      --bg-tertiary: #FFFFFF;
      --bg-dark: #2C3E3B;
      
      /* Text Colors */
      --text-primary: #1F2937;
      --text-secondary: #4B5563;
      --text-tertiary: #6B7280;
      --text-inverse: #FFFFFF;
      
      /* Accent Colors */
      --accent-success: #10B981;
      --accent-warning: #F59E0B;
      --accent-error: #EF4444;
      --accent-info: #3B82F6;
      
      /* Shadows */
      --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
      --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
      --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
      --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
      
      /* Animations */
      --transition-base: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      --transition-fast: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
    }
    
    /* Global Styles */
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
      background-color: var(--bg-primary);
      color: var(--text-primary);
      line-height: 1.6;
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
    }
    
    /* Typography */
    h1 {
      font-size: 2.5rem;
      font-weight: 700;
      line-height: 1.2;
      color: var(--text-primary);
    }
    
    h2 {
      font-size: 2rem;
      font-weight: 600;
      line-height: 1.3;
      color: var(--text-primary);
    }
    
    h3 {
      font-size: 1.5rem;
      font-weight: 600;
      line-height: 1.4;
      color: var(--text-primary);
    }
    
    p {
      font-size: 1rem;
      line-height: 1.6;
      color: var(--text-secondary);
    }
    
    /* Layout Components */
    .container {
      max-width: 1440px;
      margin: 0 auto;
      padding: 0 2rem;
    }
    
    /* Premium Navigation */
    .nav-header {
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(10px);
      border-bottom: 1px solid rgba(0, 0, 0, 0.05);
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      z-index: 1000;
      transition: var(--transition-base);
    }
    
    .nav-container {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 1rem 2rem;
    }
    
    .logo {
      display: flex;
      align-items: center;
      gap: 0.75rem;
      text-decoration: none;
    }
    
    .logo-icon {
      width: 40px;
      height: 40px;
      background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
      border-radius: 10px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-weight: bold;
      font-size: 1.25rem;
    }
    
    .logo-text {
      font-size: 1.5rem;
      font-weight: 700;
      color: var(--text-primary);
    }
    
    .nav-links {
      display: flex;
      align-items: center;
      gap: 2rem;
      list-style: none;
    }
    
    .nav-link {
      color: var(--text-secondary);
      text-decoration: none;
      font-weight: 500;
      transition: var(--transition-fast);
      position: relative;
    }
    
    .nav-link:hover {
      color: var(--primary-600);
    }
    
    .nav-link::after {
      content: '';
      position: absolute;
      bottom: -4px;
      left: 0;
      width: 0;
      height: 2px;
      background: var(--primary-500);
      transition: width 0.3s ease;
    }
    
    .nav-link:hover::after {
      width: 100%;
    }
    
    .nav-actions {
      display: flex;
      align-items: center;
      gap: 1rem;
    }
    
    /* Premium Buttons */
    .btn {
      padding: 0.75rem 1.5rem;
      border-radius: 10px;
      font-weight: 600;
      text-decoration: none;
      display: inline-flex;
      align-items: center;
      gap: 0.5rem;
      transition: var(--transition-base);
      cursor: pointer;
      border: none;
      font-size: 0.9375rem;
    }
    
    .btn-primary {
      background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
      color: white;
      box-shadow: 0 4px 14px 0 rgba(20, 184, 166, 0.25);
    }
    
    .btn-primary:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 20px 0 rgba(20, 184, 166, 0.35);
    }
    
    .btn-secondary {
      background: var(--bg-tertiary);
      color: var(--text-primary);
      border: 1px solid rgba(0, 0, 0, 0.08);
    }
    
    .btn-secondary:hover {
      background: var(--bg-secondary);
      border-color: rgba(0, 0, 0, 0.12);
    }
    
    /* Hero Section */
    .hero {
      padding: 8rem 0 4rem;
      background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
      position: relative;
      overflow: hidden;
    }
    
    .hero::before {
      content: '';
      position: absolute;
      top: -50%;
      right: -20%;
      width: 80%;
      height: 80%;
      background: radial-gradient(circle, rgba(20, 184, 166, 0.1) 0%, transparent 70%);
      border-radius: 50%;
    }
    
    .hero-content {
      position: relative;
      z-index: 1;
      text-align: center;
      max-width: 900px;
      margin: 0 auto;
    }
    
    .hero-badge {
      display: inline-flex;
      align-items: center;
      gap: 0.5rem;
      padding: 0.5rem 1rem;
      background: rgba(20, 184, 166, 0.1);
      border: 1px solid rgba(20, 184, 166, 0.2);
      border-radius: 50px;
      color: var(--primary-700);
      font-size: 0.875rem;
      font-weight: 600;
      margin-bottom: 2rem;
    }
    
    .hero-title {
      font-size: 3.5rem;
      font-weight: 800;
      line-height: 1.1;
      margin-bottom: 1.5rem;
      background: linear-gradient(135deg, var(--text-primary) 0%, var(--primary-700) 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }
    
    .hero-subtitle {
      font-size: 1.25rem;
      color: var(--text-secondary);
      margin-bottom: 3rem;
      max-width: 600px;
      margin-left: auto;
      margin-right: auto;
    }
    
    .hero-actions {
      display: flex;
      gap: 1rem;
      justify-content: center;
      flex-wrap: wrap;
    }
    
    /* Dashboard Preview */
    .dashboard-preview {
      margin-top: 4rem;
      background: var(--bg-tertiary);
      border-radius: 20px;
      box-shadow: var(--shadow-xl);
      overflow: hidden;
      border: 1px solid rgba(0, 0, 0, 0.05);
    }
    
    .dashboard-header {
      background: var(--bg-secondary);
      padding: 1.5rem;
      border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    }
    
    .dashboard-tabs {
      display: flex;
      gap: 1rem;
      margin-bottom: 2rem;
    }
    
    .tab {
      padding: 0.5rem 1rem;
      background: transparent;
      border: none;
      color: var(--text-secondary);
      font-weight: 500;
      cursor: pointer;
      border-radius: 8px;
      transition: var(--transition-fast);
    }
    
    .tab.active {
      background: var(--bg-tertiary);
      color: var(--primary-600);
      box-shadow: var(--shadow-sm);
    }
    
    .dashboard-content {
      padding: 2rem;
    }
    
    /* Metrics Grid */
    .metrics-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 1.5rem;
      margin-bottom: 2rem;
    }
    
    .metric-card {
      background: var(--bg-secondary);
      padding: 1.5rem;
      border-radius: 16px;
      border: 1px solid rgba(0, 0, 0, 0.05);
      transition: var(--transition-base);
    }
    
    .metric-card:hover {
      transform: translateY(-2px);
      box-shadow: var(--shadow-lg);
      border-color: var(--primary-200);
    }
    
    .metric-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 1rem;
    }
    
    .metric-icon {
      width: 48px;
      height: 48px;
      background: rgba(20, 184, 166, 0.1);
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: var(--primary-600);
    }
    
    .metric-value {
      font-size: 2rem;
      font-weight: 700;
      color: var(--text-primary);
      margin-bottom: 0.25rem;
    }
    
    .metric-label {
      font-size: 0.875rem;
      color: var(--text-tertiary);
      font-weight: 500;
    }
    
    .metric-change {
      display: inline-flex;
      align-items: center;
      gap: 0.25rem;
      font-size: 0.875rem;
      font-weight: 600;
      padding: 0.25rem 0.5rem;
      border-radius: 6px;
    }
    
    .metric-change.positive {
      color: var(--accent-success);
      background: rgba(16, 185, 129, 0.1);
    }
    
    .metric-change.negative {
      color: var(--accent-error);
      background: rgba(239, 68, 68, 0.1);
    }
    
    /* Chart Container */
    .chart-container {
      background: var(--bg-tertiary);
      padding: 1.5rem;
      border-radius: 16px;
      border: 1px solid rgba(0, 0, 0, 0.05);
      margin-bottom: 1.5rem;
    }
    
    .chart-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 1.5rem;
    }
    
    .chart-title {
      font-size: 1.125rem;
      font-weight: 600;
      color: var(--text-primary);
    }
    
    .chart-placeholder {
      height: 300px;
      background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-primary) 100%);
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: var(--text-tertiary);
      font-size: 0.875rem;
    }
    
    /* Features Section */
    .features {
      padding: 5rem 0;
      background: var(--bg-tertiary);
    }
    
    .features-header {
      text-align: center;
      margin-bottom: 4rem;
    }
    
    .features-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
      gap: 2rem;
    }
    
    .feature-card {
      background: var(--bg-secondary);
      padding: 2rem;
      border-radius: 20px;
      border: 1px solid rgba(0, 0, 0, 0.05);
      transition: var(--transition-base);
      cursor: pointer;
    }
    
    .feature-card:hover {
      transform: translateY(-4px);
      box-shadow: var(--shadow-xl);
      border-color: var(--primary-200);
    }
    
    .feature-icon {
      width: 64px;
      height: 64px;
      background: linear-gradient(135deg, var(--primary-400), var(--primary-600));
      border-radius: 16px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 1.5rem;
      margin-bottom: 1.5rem;
    }
    
    .feature-title {
      font-size: 1.25rem;
      font-weight: 600;
      margin-bottom: 0.75rem;
      color: var(--text-primary);
    }
    
    .feature-description {
      color: var(--text-secondary);
      line-height: 1.6;
    }
    
    /* Integration Section */
    .integrations {
      padding: 5rem 0;
      background: var(--bg-primary);
    }
    
    .integration-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
      gap: 1.5rem;
      margin-top: 3rem;
    }
    
    .integration-card {
      background: var(--bg-tertiary);
      padding: 1.5rem;
      border-radius: 16px;
      border: 1px solid rgba(0, 0, 0, 0.05);
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 1rem;
      transition: var(--transition-base);
      cursor: pointer;
    }
    
    .integration-card:hover {
      transform: translateY(-2px);
      box-shadow: var(--shadow-lg);
      border-color: var(--primary-200);
    }
    
    .integration-logo {
      width: 48px;
      height: 48px;
      background: var(--bg-secondary);
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 600;
      color: var(--primary-600);
    }
    
    .integration-name {
      font-size: 0.875rem;
      font-weight: 500;
      color: var(--text-primary);
    }
    
    /* CTA Section */
    .cta {
      padding: 5rem 0;
      background: linear-gradient(135deg, var(--primary-600) 0%, var(--primary-700) 100%);
      text-align: center;
      color: white;
    }
    
    .cta-title {
      font-size: 2.5rem;
      font-weight: 700;
      margin-bottom: 1rem;
    }
    
    .cta-subtitle {
      font-size: 1.25rem;
      opacity: 0.9;
      margin-bottom: 2rem;
    }
    
    .cta-button {
      background: white;
      color: var(--primary-600);
      padding: 1rem 2rem;
      border-radius: 12px;
      font-weight: 600;
      text-decoration: none;
      display: inline-flex;
      align-items: center;
      gap: 0.5rem;
      transition: var(--transition-base);
    }
    
    .cta-button:hover {
      transform: translateY(-2px);
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    }
    
    /* Footer */
    .footer {
      background: var(--bg-dark);
      color: var(--text-inverse);
      padding: 3rem 0 2rem;
    }
    
    .footer-content {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 2rem;
      margin-bottom: 2rem;
    }
    
    .footer-section h4 {
      font-size: 1rem;
      margin-bottom: 1rem;
      color: var(--primary-400);
    }
    
    .footer-links {
      list-style: none;
    }
    
    .footer-links li {
      margin-bottom: 0.5rem;
    }
    
    .footer-links a {
      color: rgba(255, 255, 255, 0.7);
      text-decoration: none;
      font-size: 0.875rem;
      transition: var(--transition-fast);
    }
    
    .footer-links a:hover {
      color: var(--primary-400);
    }
    
    .footer-bottom {
      padding-top: 2rem;
      border-top: 1px solid rgba(255, 255, 255, 0.1);
      text-align: center;
      font-size: 0.875rem;
      color: rgba(255, 255, 255, 0.5);
    }
    
    /* Animations */
    @keyframes fadeIn {
      from {
        opacity: 0;
        transform: translateY(20px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }
    
    .animate-in {
      animation: fadeIn 0.6s ease-out forwards;
    }
    
    /* Responsive Design */
    @media (max-width: 768px) {
      .nav-links {
        display: none;
      }
      
      .hero-title {
        font-size: 2.5rem;
      }
      
      .hero-subtitle {
        font-size: 1.125rem;
      }
      
      .metrics-grid {
        grid-template-columns: 1fr;
      }
      
      .features-grid {
        grid-template-columns: 1fr;
      }
      
      .integration-grid {
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
      }
    }
  </style>
</head>
<body>
  <!-- Premium Navigation -->
  <nav class="nav-header">
    <div class="nav-container">
      <a href="#" class="logo">
        <div class="logo-icon">DF</div>
        <span class="logo-text">DataFlow</span>
      </a>
      
      <ul class="nav-links">
        <li><a href="#features" class="nav-link">Features</a></li>
        <li><a href="#integrations" class="nav-link">Integrations</a></li>
        <li><a href="#analytics" class="nav-link">Analytics</a></li>
        <li><a href="#pricing" class="nav-link">Pricing</a></li>
        <li><a href="#resources" class="nav-link">Resources</a></li>
      </ul>
      
      <div class="nav-actions">
        <a href="#" class="btn btn-secondary">Sign In</a>
        <a href="#" class="btn btn-primary">Get Started</a>
      </div>
    </div>
  </nav>
  
  <!-- Hero Section -->
  <section class="hero">
    <div class="container">
      <div class="hero-content animate-in">
        <div class="hero-badge">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M13 2L3 14h9l-1 8 10-12h-9l1-8z"/>
          </svg>
          AI-Powered Data Intelligence
        </div>
        
        <h1 class="hero-title">Transform Your Data Into<br>Actionable Intelligence</h1>
        
        <p class="hero-subtitle">
          Connect, analyze, and visualize your data from 100+ sources with our enterprise-grade platform. 
          Make data-driven decisions with confidence.
        </p>
        
        <div class="hero-actions">
          <a href="#" class="btn btn-primary">
            Start Free Trial
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <line x1="5" y1="12" x2="19" y2="12"></line>
              <polyline points="12 5 19 12 12 19"></polyline>
            </svg>
          </a>
          <a href="#" class="btn btn-secondary">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <polygon points="5 3 19 12 5 21 5 3"></polygon>
            </svg>
            Watch Demo
          </a>
        </div>
      </div>
      
      <!-- Dashboard Preview -->
      <div class="dashboard-preview animate-in" style="animation-delay: 0.2s;">
        <div class="dashboard-header">
          <div class="dashboard-tabs">
            <button class="tab active">Overview</button>
            <button class="tab">Analytics</button>
            <button class="tab">Data Sources</button>
            <button class="tab">Reports</button>
          </div>
        </div>
        
        <div class="dashboard-content">
          <div class="metrics-grid">
            <div class="metric-card">
              <div class="metric-header">
                <div class="metric-icon">
                  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M3 3v18h18"/>
                    <path d="M18 17V9"/>
                    <path d="M13 17V5"/>
                    <path d="M8 17v-3"/>
                  </svg>
                </div>
                <span class="metric-change positive">+12.5%</span>
              </div>
              <div class="metric-value">2.4M</div>
              <div class="metric-label">Total Records Processed</div>
            </div>
            
            <div class="metric-card">
              <div class="metric-header">
                <div class="metric-icon">
                  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
                    <circle cx="12" cy="7" r="4"/>
                  </svg>
                </div>
                <span class="metric-change positive">+8.3%</span>
              </div>
              <div class="metric-value">145</div>
              <div class="metric-label">Active Data Sources</div>
            </div>
            
            <div class="metric-card">
              <div class="metric-header">
                <div class="metric-icon">
                  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M12 2L2 7l10 5 10-5-10-5z"/>
                    <path d="M2 17l10 5 10-5"/>
                    <path d="M2 12l10 5 10-5"/>
                  </svg>
                </div>
                <span class="metric-change negative">-2.1%</span>
              </div>
              <div class="metric-value">98.5%</div>
              <div class="metric-label">Data Quality Score</div>
            </div>
            
            <div class="metric-card">
              <div class="metric-header">
                <div class="metric-icon">
                  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <circle cx="12" cy="12" r="10"/>
                    <polyline points="12 6 12 12 16 14"/>
                  </svg>
                </div>
                <span class="metric-change positive">+15.2%</span>
              </div>
              <div class="metric-value">1.2s</div>
              <div class="metric-label">Avg. Processing Time</div>
            </div>
          </div>
          
          <div class="chart-container">
            <div class="chart-header">
              <h3 class="chart-title">Data Processing Trends</h3>
              <select class="btn btn-secondary" style="padding: 0.5rem 1rem; font-size: 0.875rem;">
                <option>Last 7 days</option>
                <option>Last 30 days</option>
                <option>Last 90 days</option>
              </select>
            </div>
            <div class="chart-placeholder">
              Interactive Chart Visualization
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
  
  <!-- Features Section -->
  <section class="features">
    <div class="container">
      <div class="features-header animate-in">
        <h2>Enterprise-Grade Features</h2>
        <p style="font-size: 1.125rem; margin-top: 1rem; max-width: 600px; margin-left: auto; margin-right: auto;">
          Everything you need to build a data-driven organization
        </p>
      </div>
      
      <div class="features-grid">
        <div class="feature-card animate-in" style="animation-delay: 0.1s;">
          <div class="feature-icon">
            <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"/>
              <polyline points="3.27 6.96 12 12.01 20.73 6.96"/>
              <line x1="12" y1="22.08" x2="12" y2="12"/>
            </svg>
          </div>
          <h3 class="feature-title">Real-Time Data Sync</h3>
          <p class="feature-description">
            Automatically sync data from all your sources in real-time. Support for databases, APIs, files, and cloud services.
          </p>
        </div>
        
        <div class="feature-card animate-in" style="animation-delay: 0.2s;">
          <div class="feature-icon">
            <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M9 11H3v10h6V11z"/>
              <path d="M15 3H9v18h6V3z"/>
              <path d="M21 7h-6v14h6V7z"/>
            </svg>
          </div>
          <h3 class="feature-title">Advanced Analytics</h3>
          <p class="feature-description">
            AI-powered analytics engine that automatically identifies trends, anomalies, and insights in your data.
          </p>
        </div>
        
        <div class="feature-card animate-in" style="animation-delay: 0.3s;">
          <div class="feature-icon">
            <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M12 1a3 3 0 0 0-3 3v8a3 3 0 0 0 6 0V4a3 3 0 0 0-3-3z"/>
              <path d="M19 10v2a7 7 0 0 1-14 0v-2"/>
              <line x1="12" y1="19" x2="12" y2="23"/>
              <line x1="8" y1="23" x2="16" y2="23"/>
            </svg>
          </div>
          <h3 class="feature-title">Natural Language Queries</h3>
          <p class="feature-description">
            Ask questions in plain English and get instant answers. No SQL knowledge required.
          </p>
        </div>
        
        <div class="feature-card animate-in" style="animation-delay: 0.4s;">
          <div class="feature-icon">
            <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
              <line x1="9" y1="9" x2="15" y2="15"/>
              <line x1="15" y1="9" x2="9" y2="15"/>
            </svg>
          </div>
          <h3 class="feature-title">Data Quality Monitoring</h3>
          <p class="feature-description">
            Automated data quality checks ensure your data is accurate, complete, and reliable.
          </p>
        </div>
        
        <div class="feature-card animate-in" style="animation-delay: 0.5s;">
          <div class="feature-icon">
            <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"/>
            </svg>
          </div>
          <h3 class="feature-title">Enterprise Security</h3>
          <p class="feature-description">
            Bank-level encryption, SOC2 compliance, and granular access controls keep your data secure.
          </p>
        </div>
        
        <div class="feature-card animate-in" style="animation-delay: 0.6s;">
          <div class="feature-icon">
            <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
              <polyline points="14 2 14 8 20 8"/>
              <line x1="16" y1="13" x2="8" y2="13"/>
              <line x1="16" y1="17" x2="8" y2="17"/>
              <polyline points="10 9 9 9 8 9"/>
            </svg>
          </div>
          <h3 class="feature-title">Custom Reports</h3>
          <p class="feature-description">
            Create beautiful, interactive reports and dashboards that update automatically.
          </p>
        </div>
      </div>
    </div>
  </section>
  
  <!-- Integrations Section -->
  <section class="integrations">
    <div class="container">
      <div class="features-header animate-in">
        <h2>Connect All Your Data Sources</h2>
        <p style="font-size: 1.125rem; margin-top: 1rem;">
          Seamlessly integrate with 100+ platforms and services
        </p>
      </div>
      
      <div class="integration-grid">
        <div class="integration-card animate-in" style="animation-delay: 0.1s;">
          <div class="integration-logo">SF</div>
          <span class="integration-name">Salesforce</span>
        </div>
        
        <div class="integration-card animate-in" style="animation-delay: 0.15s;">
          <div class="integration-logo">SP</div>
          <span class="integration-name">Shopify</span>
        </div>
        
        <div class="integration-card animate-in" style="animation-delay: 0.2s;">
          <div class="integration-logo">GA</div>
          <span class="integration-name">Google Analytics</span>
        </div>
        
        <div class="integration-card animate-in" style="animation-delay: 0.25s;">
          <div class="integration-logo">QB</div>
          <span class="integration-name">QuickBooks</span>
        </div>
        
        <div class="integration-card animate-in" style="animation-delay: 0.3s;">
          <div class="integration-logo">ST</div>
          <span class="integration-name">Stripe</span>
        </div>
        
        <div class="integration-card animate-in" style="animation-delay: 0.35s;">
          <div class="integration-logo">HS</div>
          <span class="integration-name">HubSpot</span>
        </div>
        
        <div class="integration-card animate-in" style="animation-delay: 0.4s;">
          <div class="integration-logo">PG</div>
          <span class="integration-name">PostgreSQL</span>
        </div>
        
        <div class="integration-card animate-in" style="animation-delay: 0.45s;">
          <div class="integration-logo">MY</div>
          <span class="integration-name">MySQL</span>
        </div>
      </div>
    </div>
  </section>
  
  <!-- CTA Section -->
  <section class="cta">
    <div class="container animate-in">
      <h2 class="cta-title">Ready to Transform Your Data?</h2>
      <p class="cta-subtitle">Join thousands of companies making smarter decisions with DataFlow</p>
      <a href="#" class="cta-button">
        Start Your Free Trial
        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <line x1="5" y1="12" x2="19" y2="12"></line>
          <polyline points="12 5 19 12 12 19"></polyline>
        </svg>
      </a>
    </div>
  </section>
  
  <!-- Footer -->
  <footer class="footer">
    <div class="container">
      <div class="footer-content">
        <div class="footer-section">
          <h4>Product</h4>
          <ul class="footer-links">
            <li><a href="#">Features</a></li>
            <li><a href="#">Integrations</a></li>
            <li><a href="#">Pricing</a></li>
            <li><a href="#">Security</a></li>
          </ul>
        </div>
        
        <div class="footer-section">
          <h4>Company</h4>
          <ul class="footer-links">
            <li><a href="#">About Us</a></li>
            <li><a href="#">Careers</a></li>
            <li><a href="#">Blog</a></li>
            <li><a href="#">Contact</a></li>
          </ul>
        </div>
        
        <div class="footer-section">
          <h4>Resources</h4>
          <ul class="footer-links">
            <li><a href="#">Documentation</a></li>
            <li><a href="#">API Reference</a></li>
            <li><a href="#">Tutorials</a></li>
            <li><a href="#">Support</a></li>
          </ul>
        </div>
        
        <div class="footer-section">
          <h4>Legal</h4>
          <ul class="footer-links">
            <li><a href="#">Privacy Policy</a></li>
            <li><a href="#">Terms of Service</a></li>
            <li><a href="#">Cookie Policy</a></li>
            <li><a href="#">GDPR</a></li>
          </ul>
        </div>
      </div>
      
      <div class="footer-bottom">
        <p>&copy; 2024 DataFlow. All rights reserved.</p>
      </div>
    </div>
  </footer>
  
  <script>
    // Intersection Observer for animations
    const observerOptions = {
      threshold: 0.1,
      rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          entry.target.style.opacity = '1';
          entry.target.style.transform = 'translateY(0)';
        }
      });
    }, observerOptions);
    
    document.querySelectorAll('.animate-in').forEach(el => {
      el.style.opacity = '0';
      el.style.transform = 'translateY(20px)';
      observer.observe(el);
    });
    
    // Smooth scroll for navigation links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
      anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
          target.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
          });
        }
      });
    });
    
    // Interactive dashboard tabs
    document.querySelectorAll('.tab').forEach(tab => {
      tab.addEventListener('click', function() {
        document.querySelectorAll('.tab').forEach(t => t.classList.remove('active'));
        this.classList.add('active');
      });
    });
    
    // Hover effects for cards
    document.querySelectorAll('.metric-card, .feature-card, .integration-card').forEach(card => {
      card.addEventListener('mouseenter', function() {
        this.style.transform = 'translateY(-4px) scale(1.02)';
      });
      
      card.addEventListener('mouseleave', function() {
        this.style.transform = 'translateY(0) scale(1)';
      });
    });
    
    // Parallax effect for hero section
    window.addEventListener('scroll', () => {
      const scrolled = window.pageYOffset;
      const hero = document.querySelector('.hero');
      if (hero) {
        hero.style.transform = `translateY(${scrolled * 0.5}px)`;
      }
    });
  </script>
</body>
</html>