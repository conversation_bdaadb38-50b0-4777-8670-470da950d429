<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Data Refinery Platform - UI Mockups</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        :root {
            --color-primary: #3b82f6;
            --color-primary-light: #dbeafe;
            --color-primary-dark: #1d4ed8;
            --color-success: #10b981;
            --color-success-light: #d1fae5;
            --color-warning: #f59e0b;
            --color-warning-light: #fef3c7;
            --color-error: #ef4444;
            --color-error-light: #fee2e2;
            --color-text: #111827;
            --color-text-secondary: #6b7280;
            --color-surface: #ffffff;
            --color-background: #f9fafb;
            --color-border: #e5e7eb;
        }

        .glassmorphism {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.18);
        }

        .metric-card {
            transition: all 0.3s ease;
            transform: translateY(0);
        }

        .metric-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
        }

        .pipeline-node {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .pipeline-node:hover {
            transform: scale(1.05);
        }

        @keyframes pulse-dot {
            0%, 100% { opacity: 1; transform: scale(1); }
            50% { opacity: 0.5; transform: scale(1.5); }
        }

        .status-indicator {
            animation: pulse-dot 2s infinite;
        }

        .data-flow-line {
            stroke-dasharray: 5, 5;
            animation: flow 2s linear infinite;
        }

        @keyframes flow {
            to { stroke-dashoffset: -10; }
        }
    </style>
</head>
<body class="bg-gray-50" x-data="{ currentPage: 'dashboard', showMobileMenu: false }">
    <!-- Navigation -->
    <nav class="fixed top-0 left-0 right-0 z-50 glassmorphism shadow-sm">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <button @click="showMobileMenu = !showMobileMenu" class="lg:hidden p-2 rounded-md text-gray-600 hover:bg-gray-100">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"/>
                        </svg>
                    </button>
                    <div class="flex items-center ml-2 lg:ml-0">
                        <div class="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                            <span class="text-white font-bold">DR</span>
                        </div>
                        <span class="ml-3 text-xl font-semibold text-gray-900">Data Refinery</span>
                    </div>
                </div>
                
                <!-- Desktop Navigation -->
                <div class="hidden lg:flex items-center space-x-8">
                    <button @click="currentPage = 'dashboard'" 
                            :class="currentPage === 'dashboard' ? 'text-blue-600 border-b-2 border-blue-600' : 'text-gray-600 hover:text-gray-900'"
                            class="px-1 py-5 font-medium transition">Dashboard</button>
                    <button @click="currentPage = 'datasources'" 
                            :class="currentPage === 'datasources' ? 'text-blue-600 border-b-2 border-blue-600' : 'text-gray-600 hover:text-gray-900'"
                            class="px-1 py-5 font-medium transition">Data Sources</button>
                    <button @click="currentPage = 'pipelines'" 
                            :class="currentPage === 'pipelines' ? 'text-blue-600 border-b-2 border-blue-600' : 'text-gray-600 hover:text-gray-900'"
                            class="px-1 py-5 font-medium transition">Pipelines</button>
                    <button @click="currentPage = 'analytics'" 
                            :class="currentPage === 'analytics' ? 'text-blue-600 border-b-2 border-blue-600' : 'text-gray-600 hover:text-gray-900'"
                            class="px-1 py-5 font-medium transition">Analytics</button>
                    <button @click="currentPage = 'ai-insights'" 
                            :class="currentPage === 'ai-insights' ? 'text-blue-600 border-b-2 border-blue-600' : 'text-gray-600 hover:text-gray-900'"
                            class="px-1 py-5 font-medium transition">AI Insights</button>
                </div>

                <!-- Right side items -->
                <div class="flex items-center space-x-4">
                    <button class="p-2 text-gray-600 hover:bg-gray-100 rounded-lg transition">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"/>
                        </svg>
                    </button>
                    <div class="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center text-white font-semibold">
                        KJ
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- Mobile Menu -->
    <div x-show="showMobileMenu" x-transition class="lg:hidden fixed inset-0 z-40 bg-gray-900/50" @click="showMobileMenu = false">
        <div class="fixed inset-y-0 left-0 w-64 bg-white shadow-xl" @click.stop>
            <div class="p-6">
                <div class="flex items-center mb-8">
                    <div class="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                        <span class="text-white font-bold">DR</span>
                    </div>
                    <span class="ml-3 text-xl font-semibold text-gray-900">Data Refinery</span>
                </div>
                <nav class="space-y-2">
                    <button @click="currentPage = 'dashboard'; showMobileMenu = false" 
                            :class="currentPage === 'dashboard' ? 'bg-blue-50 text-blue-600' : 'text-gray-700 hover:bg-gray-50'"
                            class="w-full flex items-center px-3 py-2 rounded-lg font-medium transition">
                        <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"/>
                        </svg>
                        Dashboard
                    </button>
                    <button @click="currentPage = 'datasources'; showMobileMenu = false" 
                            :class="currentPage === 'datasources' ? 'bg-blue-50 text-blue-600' : 'text-gray-700 hover:bg-gray-50'"
                            class="w-full flex items-center px-3 py-2 rounded-lg font-medium transition">
                        <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4"/>
                        </svg>
                        Data Sources
                    </button>
                    <button @click="currentPage = 'pipelines'; showMobileMenu = false" 
                            :class="currentPage === 'pipelines' ? 'bg-blue-50 text-blue-600' : 'text-gray-700 hover:bg-gray-50'"
                            class="w-full flex items-center px-3 py-2 rounded-lg font-medium transition">
                        <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
                        </svg>
                        Pipelines
                    </button>
                    <button @click="currentPage = 'analytics'; showMobileMenu = false" 
                            :class="currentPage === 'analytics' ? 'bg-blue-50 text-blue-600' : 'text-gray-700 hover:bg-gray-50'"
                            class="w-full flex items-center px-3 py-2 rounded-lg font-medium transition">
                        <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
                        </svg>
                        Analytics
                    </button>
                    <button @click="currentPage = 'ai-insights'; showMobileMenu = false" 
                            :class="currentPage === 'ai-insights' ? 'bg-blue-50 text-blue-600' : 'text-gray-700 hover:bg-gray-50'"
                            class="w-full flex items-center px-3 py-2 rounded-lg font-medium transition">
                        <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"/>
                        </svg>
                        AI Insights
                    </button>
                </nav>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <main class="pt-20 pb-8 px-4 sm:px-6 lg:px-8 max-w-7xl mx-auto">
        <!-- Dashboard Page -->
        <div x-show="currentPage === 'dashboard'" x-transition>
            <div class="mb-8">
                <h1 class="text-3xl font-bold text-gray-900 mb-2">Data Refinery Dashboard</h1>
                <p class="text-gray-600">Real-time data processing insights and AI-powered analytics</p>
            </div>

            <!-- KPI Metrics -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <div class="metric-card bg-white rounded-xl p-6 shadow-sm border border-gray-100">
                    <div class="flex items-center justify-between mb-4">
                        <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                            <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4"/>
                            </svg>
                        </div>
                        <span class="text-green-500 text-sm font-medium">+12%</span>
                    </div>
                    <h3 class="text-sm font-medium text-gray-600 mb-1">Data Sources</h3>
                    <p class="text-2xl font-bold text-gray-900">24</p>
                    <p class="text-sm text-gray-500 mt-1">18 connected</p>
                </div>

                <div class="metric-card bg-white rounded-xl p-6 shadow-sm border border-gray-100">
                    <div class="flex items-center justify-between mb-4">
                        <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                            <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
                            </svg>
                        </div>
                        <span class="text-green-500 text-sm font-medium">+8.2%</span>
                    </div>
                    <h3 class="text-sm font-medium text-gray-600 mb-1">Records Processed</h3>
                    <p class="text-2xl font-bold text-gray-900">2.4M</p>
                    <p class="text-sm text-gray-500 mt-1">This month</p>
                </div>

                <div class="metric-card bg-white rounded-xl p-6 shadow-sm border border-gray-100">
                    <div class="flex items-center justify-between mb-4">
                        <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                            <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                            </svg>
                        </div>
                        <span class="text-purple-500 text-sm font-medium">Excellent</span>
                    </div>
                    <h3 class="text-sm font-medium text-gray-600 mb-1">Data Quality Score</h3>
                    <p class="text-2xl font-bold text-gray-900">94%</p>
                    <p class="text-sm text-gray-500 mt-1">↑ 2% from last week</p>
                </div>

                <div class="metric-card bg-white rounded-xl p-6 shadow-sm border border-gray-100">
                    <div class="flex items-center justify-between mb-4">
                        <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                            <svg class="w-6 h-6 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
                            </svg>
                        </div>
                        <div class="flex items-center">
                            <div class="w-2 h-2 bg-green-500 rounded-full status-indicator"></div>
                            <span class="text-green-500 text-sm font-medium ml-2">Active</span>
                        </div>
                    </div>
                    <h3 class="text-sm font-medium text-gray-600 mb-1">Active Pipelines</h3>
                    <p class="text-2xl font-bold text-gray-900">12</p>
                    <p class="text-sm text-gray-500 mt-1">3 scheduled</p>
                </div>
            </div>

            <!-- AI Insights Panel -->
            <div class="bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl p-6 mb-8 text-white">
                <div class="flex items-center justify-between mb-4">
                    <h2 class="text-xl font-semibold flex items-center">
                        <svg class="w-6 h-6 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"/>
                        </svg>
                        AI-Powered Insights
                    </h2>
                    <button class="px-4 py-2 bg-white/20 hover:bg-white/30 rounded-lg text-sm font-medium transition">
                        View All
                    </button>
                </div>
                <div class="grid md:grid-cols-3 gap-4">
                    <div class="bg-white/10 backdrop-blur rounded-lg p-4">
                        <div class="flex items-center justify-between mb-2">
                            <span class="text-orange-300 text-sm font-medium">Critical</span>
                            <span class="text-xs opacity-75">95% confidence</span>
                        </div>
                        <p class="text-sm">Data completeness dropped to 78% in Shopify integration. Missing product descriptions detected.</p>
                    </div>
                    <div class="bg-white/10 backdrop-blur rounded-lg p-4">
                        <div class="flex items-center justify-between mb-2">
                            <span class="text-yellow-300 text-sm font-medium">Optimization</span>
                            <span class="text-xs opacity-75">88% confidence</span>
                        </div>
                        <p class="text-sm">Pipeline efficiency can improve 40% by adjusting sync frequency during off-peak hours.</p>
                    </div>
                    <div class="bg-white/10 backdrop-blur rounded-lg p-4">
                        <div class="flex items-center justify-between mb-2">
                            <span class="text-green-300 text-sm font-medium">Pattern</span>
                            <span class="text-xs opacity-75">92% confidence</span>
                        </div>
                        <p class="text-sm">Weekend transaction volume increased 15%. Consider scaling resources accordingly.</p>
                    </div>
                </div>
            </div>

            <!-- Charts Section -->
            <div class="grid md:grid-cols-2 gap-6 mb-8">
                <div class="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-semibold text-gray-900">Processing Volume</h3>
                        <select class="text-sm border-gray-300 rounded-lg">
                            <option>Last 7 days</option>
                            <option>Last 30 days</option>
                            <option>Last 90 days</option>
                        </select>
                    </div>
                    <canvas id="processingChart" width="400" height="200"></canvas>
                </div>
                <div class="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-semibold text-gray-900">Data Quality Distribution</h3>
                        <button class="text-sm text-blue-600 hover:text-blue-700">Export</button>
                    </div>
                    <canvas id="qualityChart" width="400" height="200"></canvas>
                </div>
            </div>

            <!-- Recent Activity -->
            <div class="bg-white rounded-xl shadow-sm border border-gray-100">
                <div class="p-6 border-b border-gray-100">
                    <h3 class="text-lg font-semibold text-gray-900">Recent Pipeline Activity</h3>
                </div>
                <div class="divide-y divide-gray-100">
                    <div class="p-4 hover:bg-gray-50 transition">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center mr-4">
                                    <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                                    </svg>
                                </div>
                                <div>
                                    <p class="font-medium text-gray-900">Shopify Customer Sync</p>
                                    <p class="text-sm text-gray-500">Completed successfully • 12,847 records</p>
                                </div>
                            </div>
                            <span class="text-sm text-gray-500">2 mins ago</span>
                        </div>
                    </div>
                    <div class="p-4 hover:bg-gray-50 transition">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-4">
                                    <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
                                    </svg>
                                </div>
                                <div>
                                    <p class="font-medium text-gray-900">Google Analytics Import</p>
                                    <p class="text-sm text-gray-500">In progress • 45% complete</p>
                                </div>
                            </div>
                            <span class="text-sm text-gray-500">5 mins ago</span>
                        </div>
                    </div>
                    <div class="p-4 hover:bg-gray-50 transition">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center mr-4">
                                    <svg class="w-5 h-5 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L12.732 4c-.77-1.667-2.308-1.667-3.08 0L3.34 16c-.77 1.333.192 3 1.732 3z"/>
                                    </svg>
                                </div>
                                <div>
                                    <p class="font-medium text-gray-900">Xero Financial Data</p>
                                    <p class="text-sm text-gray-500">Warning: Missing tax codes in 234 transactions</p>
                                </div>
                            </div>
                            <span class="text-sm text-gray-500">15 mins ago</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Data Sources Page -->
        <div x-show="currentPage === 'datasources'" x-transition>
            <div class="mb-8 flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900 mb-2">Data Sources</h1>
                    <p class="text-gray-600">Connect and manage your data integrations</p>
                </div>
                <button class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition flex items-center">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"/>
                    </svg>
                    Add Data Source
                </button>
            </div>

            <!-- Filter Tabs -->
            <div class="border-b border-gray-200 mb-6">
                <nav class="flex space-x-8">
                    <button class="border-b-2 border-blue-600 text-blue-600 py-2 px-1 font-medium">All Sources</button>
                    <button class="text-gray-500 hover:text-gray-700 py-2 px-1 font-medium">Databases</button>
                    <button class="text-gray-500 hover:text-gray-700 py-2 px-1 font-medium">APIs</button>
                    <button class="text-gray-500 hover:text-gray-700 py-2 px-1 font-medium">Files</button>
                    <button class="text-gray-500 hover:text-gray-700 py-2 px-1 font-medium">Cloud Storage</button>
                </nav>
            </div>

            <!-- Data Sources Grid -->
            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                <!-- Shopify Card -->
                <div class="bg-white rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition">
                    <div class="p-6">
                        <div class="flex items-center justify-between mb-4">
                            <div class="flex items-center">
                                <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                                    <span class="text-green-600 font-bold">SH</span>
                                </div>
                                <div class="ml-4">
                                    <h3 class="font-semibold text-gray-900">Shopify Store</h3>
                                    <p class="text-sm text-gray-500">E-commerce API</p>
                                </div>
                            </div>
                            <div class="flex items-center">
                                <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                                <span class="text-sm text-green-600 ml-2">Connected</span>
                            </div>
                        </div>
                        <div class="space-y-3">
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-600">Last sync</span>
                                <span class="text-gray-900">2 hours ago</span>
                            </div>
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-600">Records</span>
                                <span class="text-gray-900">45,231</span>
                            </div>
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-600">Sync frequency</span>
                                <span class="text-gray-900">Every 4 hours</span>
                            </div>
                        </div>
                        <div class="mt-4 flex space-x-2">
                            <button class="flex-1 px-3 py-2 bg-blue-50 text-blue-600 rounded-lg hover:bg-blue-100 transition text-sm font-medium">
                                Sync Now
                            </button>
                            <button class="flex-1 px-3 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition text-sm font-medium">
                                Configure
                            </button>
                        </div>
                    </div>
                </div>

                <!-- PostgreSQL Card -->
                <div class="bg-white rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition">
                    <div class="p-6">
                        <div class="flex items-center justify-between mb-4">
                            <div class="flex items-center">
                                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                                    <span class="text-blue-600 font-bold">PG</span>
                                </div>
                                <div class="ml-4">
                                    <h3 class="font-semibold text-gray-900">PostgreSQL</h3>
                                    <p class="text-sm text-gray-500">Production Database</p>
                                </div>
                            </div>
                            <div class="flex items-center">
                                <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                                <span class="text-sm text-green-600 ml-2">Connected</span>
                            </div>
                        </div>
                        <div class="space-y-3">
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-600">Last sync</span>
                                <span class="text-gray-900">15 mins ago</span>
                            </div>
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-600">Tables</span>
                                <span class="text-gray-900">24</span>
                            </div>
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-600">Sync mode</span>
                                <span class="text-gray-900">Real-time CDC</span>
                            </div>
                        </div>
                        <div class="mt-4 flex space-x-2">
                            <button class="flex-1 px-3 py-2 bg-blue-50 text-blue-600 rounded-lg hover:bg-blue-100 transition text-sm font-medium">
                                View Schema
                            </button>
                            <button class="flex-1 px-3 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition text-sm font-medium">
                                Configure
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Google Analytics Card -->
                <div class="bg-white rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition">
                    <div class="p-6">
                        <div class="flex items-center justify-between mb-4">
                            <div class="flex items-center">
                                <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                                    <span class="text-orange-600 font-bold">GA</span>
                                </div>
                                <div class="ml-4">
                                    <h3 class="font-semibold text-gray-900">Google Analytics</h3>
                                    <p class="text-sm text-gray-500">Web Analytics</p>
                                </div>
                            </div>
                            <div class="flex items-center">
                                <div class="w-2 h-2 bg-yellow-500 rounded-full"></div>
                                <span class="text-sm text-yellow-600 ml-2">Syncing</span>
                            </div>
                        </div>
                        <div class="space-y-3">
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-600">Progress</span>
                                <span class="text-gray-900">67%</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div class="bg-blue-600 h-2 rounded-full" style="width: 67%"></div>
                            </div>
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-600">Est. completion</span>
                                <span class="text-gray-900">~5 mins</span>
                            </div>
                        </div>
                        <div class="mt-4 flex space-x-2">
                            <button class="flex-1 px-3 py-2 bg-gray-100 text-gray-400 rounded-lg cursor-not-allowed text-sm font-medium" disabled>
                                Syncing...
                            </button>
                            <button class="flex-1 px-3 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition text-sm font-medium">
                                View Logs
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Add New Source Card -->
                <button class="bg-gray-50 rounded-xl border-2 border-dashed border-gray-300 hover:border-gray-400 hover:bg-gray-100 transition min-h-[240px] flex flex-col items-center justify-center">
                    <svg class="w-12 h-12 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"/>
                    </svg>
                    <span class="text-gray-600 font-medium">Add New Data Source</span>
                    <span class="text-sm text-gray-500 mt-1">200+ integrations available</span>
                </button>
            </div>
        </div>

        <!-- Pipelines Page -->
        <div x-show="currentPage === 'pipelines'" x-transition>
            <div class="mb-8 flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900 mb-2">ETL Pipelines</h1>
                    <p class="text-gray-600">Design and monitor your data transformation workflows</p>
                </div>
                <button class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition flex items-center">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"/>
                    </svg>
                    Create Pipeline
                </button>
            </div>

            <!-- Pipeline Builder -->
            <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6 mb-8">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Visual Pipeline Builder</h3>
                <div class="bg-gray-50 rounded-lg p-8 flex items-center justify-center min-h-[400px] relative overflow-hidden">
                    <!-- Pipeline Flow Visualization -->
                    <svg class="w-full h-full max-w-4xl" viewBox="0 0 800 300">
                        <!-- Flow Lines -->
                        <line x1="150" y1="150" x2="250" y2="150" stroke="#e5e7eb" stroke-width="2" class="data-flow-line"/>
                        <line x1="350" y1="150" x2="450" y2="150" stroke="#e5e7eb" stroke-width="2" class="data-flow-line"/>
                        <line x1="550" y1="150" x2="650" y2="150" stroke="#e5e7eb" stroke-width="2" class="data-flow-line"/>
                        
                        <!-- Source Node -->
                        <g class="pipeline-node cursor-pointer">
                            <rect x="50" y="100" width="100" height="100" rx="8" fill="#dbeafe" stroke="#3b82f6" stroke-width="2"/>
                            <text x="100" y="140" text-anchor="middle" class="text-sm font-medium fill-blue-600">Source</text>
                            <text x="100" y="160" text-anchor="middle" class="text-xs fill-blue-600">Shopify API</text>
                        </g>
                        
                        <!-- Transform Node 1 -->
                        <g class="pipeline-node cursor-pointer">
                            <rect x="250" y="100" width="100" height="100" rx="8" fill="#fef3c7" stroke="#f59e0b" stroke-width="2"/>
                            <text x="300" y="140" text-anchor="middle" class="text-sm font-medium fill-orange-600">Transform</text>
                            <text x="300" y="160" text-anchor="middle" class="text-xs fill-orange-600">Clean Data</text>
                        </g>
                        
                        <!-- Transform Node 2 -->
                        <g class="pipeline-node cursor-pointer">
                            <rect x="450" y="100" width="100" height="100" rx="8" fill="#d1fae5" stroke="#10b981" stroke-width="2"/>
                            <text x="500" y="140" text-anchor="middle" class="text-sm font-medium fill-green-600">Enrich</text>
                            <text x="500" y="160" text-anchor="middle" class="text-xs fill-green-600">Add Metadata</text>
                        </g>
                        
                        <!-- Destination Node -->
                        <g class="pipeline-node cursor-pointer">
                            <rect x="650" y="100" width="100" height="100" rx="8" fill="#e0e7ff" stroke="#6366f1" stroke-width="2"/>
                            <text x="700" y="140" text-anchor="middle" class="text-sm font-medium fill-indigo-600">Destination</text>
                            <text x="700" y="160" text-anchor="middle" class="text-xs fill-indigo-600">Data Warehouse</text>
                        </g>
                    </svg>
                </div>
                <div class="mt-4 flex justify-end space-x-3">
                    <button class="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition">
                        Save as Template
                    </button>
                    <button class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition">
                        Deploy Pipeline
                    </button>
                </div>
            </div>

            <!-- Active Pipelines -->
            <div class="bg-white rounded-xl shadow-sm border border-gray-100">
                <div class="p-6 border-b border-gray-100">
                    <h3 class="text-lg font-semibold text-gray-900">Active Pipelines</h3>
                </div>
                <div class="overflow-x-auto">
                    <table class="w-full">
                        <thead>
                            <tr class="border-b border-gray-100">
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Pipeline Name</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Source → Destination</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Schedule</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Last Run</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="divide-y divide-gray-100">
                            <tr class="hover:bg-gray-50 transition">
                                <td class="px-6 py-4">
                                    <div class="font-medium text-gray-900">Customer 360 View</div>
                                </td>
                                <td class="px-6 py-4">
                                    <div class="text-sm text-gray-600">Shopify → Warehouse</div>
                                </td>
                                <td class="px-6 py-4">
                                    <div class="text-sm text-gray-600">Every 4 hours</div>
                                </td>
                                <td class="px-6 py-4">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        Running
                                    </span>
                                </td>
                                <td class="px-6 py-4">
                                    <div class="text-sm text-gray-600">2 hours ago</div>
                                </td>
                                <td class="px-6 py-4">
                                    <button class="text-blue-600 hover:text-blue-700 text-sm font-medium">View Details</button>
                                </td>
                            </tr>
                            <tr class="hover:bg-gray-50 transition">
                                <td class="px-6 py-4">
                                    <div class="font-medium text-gray-900">Financial Analytics</div>
                                </td>
                                <td class="px-6 py-4">
                                    <div class="text-sm text-gray-600">Xero → BigQuery</div>
                                </td>
                                <td class="px-6 py-4">
                                    <div class="text-sm text-gray-600">Daily at 2 AM</div>
                                </td>
                                <td class="px-6 py-4">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                        Scheduled
                                    </span>
                                </td>
                                <td class="px-6 py-4">
                                    <div class="text-sm text-gray-600">Yesterday</div>
                                </td>
                                <td class="px-6 py-4">
                                    <button class="text-blue-600 hover:text-blue-700 text-sm font-medium">View Details</button>
                                </td>
                            </tr>
                            <tr class="hover:bg-gray-50 transition">
                                <td class="px-6 py-4">
                                    <div class="font-medium text-gray-900">Marketing Attribution</div>
                                </td>
                                <td class="px-6 py-4">
                                    <div class="text-sm text-gray-600">GA4 → Snowflake</div>
                                </td>
                                <td class="px-6 py-4">
                                    <div class="text-sm text-gray-600">Real-time</div>
                                </td>
                                <td class="px-6 py-4">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                        Warning
                                    </span>
                                </td>
                                <td class="px-6 py-4">
                                    <div class="text-sm text-gray-600">15 mins ago</div>
                                </td>
                                <td class="px-6 py-4">
                                    <button class="text-blue-600 hover:text-blue-700 text-sm font-medium">View Details</button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Analytics Page -->
        <div x-show="currentPage === 'analytics'" x-transition>
            <div class="mb-8">
                <h1 class="text-3xl font-bold text-gray-900 mb-2">Analytics Dashboard</h1>
                <p class="text-gray-600">Comprehensive business intelligence and reporting</p>
            </div>

            <!-- Date Range Selector -->
            <div class="mb-6 flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <button class="px-4 py-2 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition flex items-center">
                        <svg class="w-5 h-5 mr-2 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                        </svg>
                        Last 30 Days
                    </button>
                    <button class="px-4 py-2 text-gray-600 hover:text-gray-900 transition">Compare to Previous Period</button>
                </div>
                <div class="flex items-center space-x-3">
                    <button class="px-4 py-2 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition">
                        Export Report
                    </button>
                    <button class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition">
                        Create Dashboard
                    </button>
                </div>
            </div>

            <!-- Revenue Analytics -->
            <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6 mb-6">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-lg font-semibold text-gray-900">Revenue Overview</h3>
                    <div class="flex items-center space-x-4">
                        <span class="text-2xl font-bold text-gray-900">$847,293</span>
                        <span class="text-sm text-green-600 font-medium">↑ 12.5%</span>
                    </div>
                </div>
                <canvas id="revenueChart" width="800" height="300"></canvas>
            </div>

            <!-- Analytics Grid -->
            <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
                <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                    <div class="flex items-center justify-between mb-4">
                        <h4 class="text-sm font-medium text-gray-600">Total Customers</h4>
                        <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"/>
                        </svg>
                    </div>
                    <p class="text-2xl font-bold text-gray-900 mb-2">12,847</p>
                    <p class="text-sm text-green-600">+8.3% from last month</p>
                </div>

                <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                    <div class="flex items-center justify-between mb-4">
                        <h4 class="text-sm font-medium text-gray-600">Average Order Value</h4>
                        <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                        </svg>
                    </div>
                    <p class="text-2xl font-bold text-gray-900 mb-2">$65.84</p>
                    <p class="text-sm text-green-600">+3.2% from last month</p>
                </div>

                <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                    <div class="flex items-center justify-between mb-4">
                        <h4 class="text-sm font-medium text-gray-600">Conversion Rate</h4>
                        <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
                        </svg>
                    </div>
                    <p class="text-2xl font-bold text-gray-900 mb-2">3.84%</p>
                    <p class="text-sm text-red-600">-0.5% from last month</p>
                </div>

                <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                    <div class="flex items-center justify-between mb-4">
                        <h4 class="text-sm font-medium text-gray-600">Customer LTV</h4>
                        <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"/>
                        </svg>
                    </div>
                    <p class="text-2xl font-bold text-gray-900 mb-2">$284.50</p>
                    <p class="text-sm text-green-600">+15.2% from last month</p>
                </div>
            </div>

            <!-- Customer Segments -->
            <div class="grid md:grid-cols-2 gap-6">
                <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Customer Segments</h3>
                    <canvas id="segmentsChart" width="400" height="300"></canvas>
                </div>
                <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Top Products</h3>
                    <div class="space-y-4">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-gray-100 rounded-lg mr-3"></div>
                                <div>
                                    <p class="font-medium text-gray-900">Premium Widget Pro</p>
                                    <p class="text-sm text-gray-500">SKU: WGT-001</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <p class="font-medium text-gray-900">$45,230</p>
                                <p class="text-sm text-green-600">+12%</p>
                            </div>
                        </div>
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-gray-100 rounded-lg mr-3"></div>
                                <div>
                                    <p class="font-medium text-gray-900">Smart Gadget X</p>
                                    <p class="text-sm text-gray-500">SKU: GDT-042</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <p class="font-medium text-gray-900">$38,190</p>
                                <p class="text-sm text-green-600">+8%</p>
                            </div>
                        </div>
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-gray-100 rounded-lg mr-3"></div>
                                <div>
                                    <p class="font-medium text-gray-900">Essential Bundle</p>
                                    <p class="text-sm text-gray-500">SKU: BND-003</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <p class="font-medium text-gray-900">$32,840</p>
                                <p class="text-sm text-red-600">-3%</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- AI Insights Page -->
        <div x-show="currentPage === 'ai-insights'" x-transition>
            <div class="mb-8">
                <h1 class="text-3xl font-bold text-gray-900 mb-2">AI-Powered Insights</h1>
                <p class="text-gray-600">Intelligent predictions, anomaly detection, and automated recommendations</p>
            </div>

            <!-- AI Assistant Panel -->
            <div class="bg-gradient-to-r from-purple-600 to-blue-600 rounded-xl p-6 mb-8 text-white">
                <div class="flex items-center justify-between mb-4">
                    <h2 class="text-xl font-semibold flex items-center">
                        <svg class="w-6 h-6 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"/>
                        </svg>
                        AI Business Intelligence Agent
                    </h2>
                    <button class="px-4 py-2 bg-white/20 hover:bg-white/30 rounded-lg text-sm font-medium transition">
                        Configure Agent
                    </button>
                </div>
                <div class="bg-white/10 backdrop-blur rounded-lg p-4 mb-4">
                    <p class="text-white/90 mb-3">Ask me anything about your data:</p>
                    <div class="flex space-x-2">
                        <input type="text" placeholder="What were my top performing products last quarter?" 
                               class="flex-1 px-4 py-2 bg-white/20 rounded-lg placeholder-white/60 text-white border border-white/20 focus:outline-none focus:border-white/40">
                        <button class="px-6 py-2 bg-white text-purple-600 rounded-lg font-medium hover:bg-white/90 transition">
                            Ask AI
                        </button>
                    </div>
                </div>
                <div class="grid md:grid-cols-3 gap-4">
                    <button class="bg-white/10 backdrop-blur rounded-lg p-3 text-left hover:bg-white/20 transition">
                        <span class="text-sm">Generate weekly executive report</span>
                    </button>
                    <button class="bg-white/10 backdrop-blur rounded-lg p-3 text-left hover:bg-white/20 transition">
                        <span class="text-sm">Predict next month's revenue</span>
                    </button>
                    <button class="bg-white/10 backdrop-blur rounded-lg p-3 text-left hover:bg-white/20 transition">
                        <span class="text-sm">Find customer churn risks</span>
                    </button>
                </div>
            </div>

            <!-- Predictions & Forecasts -->
            <div class="grid md:grid-cols-2 gap-6 mb-8">
                <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Revenue Forecast</h3>
                    <div class="mb-4">
                        <div class="flex items-center justify-between mb-2">
                            <span class="text-sm text-gray-600">Next 30 days</span>
                            <span class="text-sm font-medium text-gray-900">95% confidence</span>
                        </div>
                        <div class="text-3xl font-bold text-gray-900 mb-1">$923,450</div>
                        <div class="text-sm text-green-600">+8.9% vs current month</div>
                    </div>
                    <canvas id="forecastChart" width="400" height="200"></canvas>
                </div>

                <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Anomaly Detection</h3>
                    <div class="space-y-3">
                        <div class="p-3 bg-red-50 border border-red-200 rounded-lg">
                            <div class="flex items-center justify-between mb-1">
                                <span class="text-sm font-medium text-red-900">Unusual Order Pattern</span>
                                <span class="text-xs text-red-600">2 hours ago</span>
                            </div>
                            <p class="text-sm text-red-700">Spike in orders from new IP range - possible fraud risk</p>
                        </div>
                        <div class="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                            <div class="flex items-center justify-between mb-1">
                                <span class="text-sm font-medium text-yellow-900">Inventory Alert</span>
                                <span class="text-xs text-yellow-600">5 hours ago</span>
                            </div>
                            <p class="text-sm text-yellow-700">Widget Pro stock depleting 40% faster than usual</p>
                        </div>
                        <div class="p-3 bg-blue-50 border border-blue-200 rounded-lg">
                            <div class="flex items-center justify-between mb-1">
                                <span class="text-sm font-medium text-blue-900">Traffic Surge</span>
                                <span class="text-xs text-blue-600">1 day ago</span>
                            </div>
                            <p class="text-sm text-blue-700">Organic traffic up 125% - viral social media mention detected</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- AI Recommendations -->
            <div class="bg-white rounded-xl shadow-sm border border-gray-100">
                <div class="p-6 border-b border-gray-100">
                    <h3 class="text-lg font-semibold text-gray-900">AI-Generated Recommendations</h3>
                </div>
                <div class="divide-y divide-gray-100">
                    <div class="p-6">
                        <div class="flex items-start">
                            <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center flex-shrink-0">
                                <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"/>
                                </svg>
                            </div>
                            <div class="ml-4 flex-1">
                                <h4 class="font-medium text-gray-900 mb-1">Optimize Marketing Spend</h4>
                                <p class="text-sm text-gray-600 mb-3">
                                    Reallocate 20% of Facebook ad budget to Google Shopping based on ROAS analysis. 
                                    Expected revenue increase: $12,400/month
                                </p>
                                <div class="flex items-center space-x-3">
                                    <button class="px-4 py-2 bg-green-600 text-white rounded-lg text-sm font-medium hover:bg-green-700 transition">
                                        Implement
                                    </button>
                                    <button class="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg text-sm font-medium hover:bg-gray-50 transition">
                                        View Analysis
                                    </button>
                                    <span class="text-sm text-gray-500">Impact: High • Confidence: 92%</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="p-6">
                        <div class="flex items-start">
                            <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center flex-shrink-0">
                                <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                </svg>
                            </div>
                            <div class="ml-4 flex-1">
                                <h4 class="font-medium text-gray-900 mb-1">Adjust Inventory Levels</h4>
                                <p class="text-sm text-gray-600 mb-3">
                                    Increase Premium Widget Pro stock by 40% before Black Friday based on historical patterns 
                                    and current demand trajectory
                                </p>
                                <div class="flex items-center space-x-3">
                                    <button class="px-4 py-2 bg-blue-600 text-white rounded-lg text-sm font-medium hover:bg-blue-700 transition">
                                        Schedule Order
                                    </button>
                                    <button class="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg text-sm font-medium hover:bg-gray-50 transition">
                                        Adjust Forecast
                                    </button>
                                    <span class="text-sm text-gray-500">Impact: Medium • Confidence: 88%</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="p-6">
                        <div class="flex items-start">
                            <div class="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center flex-shrink-0">
                                <svg class="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"/>
                                </svg>
                            </div>
                            <div class="ml-4 flex-1">
                                <h4 class="font-medium text-gray-900 mb-1">Re-engage Dormant Customers</h4>
                                <p class="text-sm text-gray-600 mb-3">
                                    Launch targeted win-back campaign for 1,243 customers who haven't purchased in 90+ days. 
                                    Predicted conversion rate: 18%
                                </p>
                                <div class="flex items-center space-x-3">
                                    <button class="px-4 py-2 bg-purple-600 text-white rounded-lg text-sm font-medium hover:bg-purple-700 transition">
                                        Create Campaign
                                    </button>
                                    <button class="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg text-sm font-medium hover:bg-gray-50 transition">
                                        View Segments
                                    </button>
                                    <span class="text-sm text-gray-500">Impact: Medium • Confidence: 85%</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Chart Initialization Scripts -->
    <script>
        // Initialize charts when DOM is loaded
        document.addEventListener('DOMContentLoaded', function() {
            // Processing Volume Chart
            const processingCtx = document.getElementById('processingChart');
            if (processingCtx) {
                new Chart(processingCtx, {
                    type: 'line',
                    data: {
                        labels: ['Week 1', 'Week 2', 'Week 3', 'Week 4'],
                        datasets: [{
                            label: 'Records Processed',
                            data: [425000, 542000, 638000, 756000],
                            borderColor: '#3b82f6',
                            backgroundColor: 'rgba(59, 130, 246, 0.1)',
                            tension: 0.4
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: { display: false }
                        }
                    }
                });
            }

            // Quality Distribution Chart
            const qualityCtx = document.getElementById('qualityChart');
            if (qualityCtx) {
                new Chart(qualityCtx, {
                    type: 'doughnut',
                    data: {
                        labels: ['Excellent', 'Good', 'Fair', 'Poor'],
                        datasets: [{
                            data: [65, 25, 8, 2],
                            backgroundColor: ['#10b981', '#3b82f6', '#f59e0b', '#ef4444']
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false
                    }
                });
            }

            // Revenue Chart
            const revenueCtx = document.getElementById('revenueChart');
            if (revenueCtx) {
                new Chart(revenueCtx, {
                    type: 'line',
                    data: {
                        labels: Array.from({length: 30}, (_, i) => `Day ${i + 1}`),
                        datasets: [{
                            label: 'Revenue',
                            data: Array.from({length: 30}, () => Math.floor(Math.random() * 20000) + 20000),
                            borderColor: '#3b82f6',
                            backgroundColor: 'rgba(59, 130, 246, 0.1)',
                            tension: 0.4
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: { display: false }
                        }
                    }
                });
            }

            // Segments Chart
            const segmentsCtx = document.getElementById('segmentsChart');
            if (segmentsCtx) {
                new Chart(segmentsCtx, {
                    type: 'pie',
                    data: {
                        labels: ['VIP Customers', 'Regular Buyers', 'New Customers', 'At Risk'],
                        datasets: [{
                            data: [15, 45, 30, 10],
                            backgroundColor: ['#8b5cf6', '#3b82f6', '#10b981', '#f59e0b']
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false
                    }
                });
            }

            // Forecast Chart
            const forecastCtx = document.getElementById('forecastChart');
            if (forecastCtx) {
                new Chart(forecastCtx, {
                    type: 'line',
                    data: {
                        labels: Array.from({length: 30}, (_, i) => `Day ${i + 1}`),
                        datasets: [{
                            label: 'Actual',
                            data: Array.from({length: 15}, () => Math.floor(Math.random() * 20000) + 25000),
                            borderColor: '#3b82f6',
                            backgroundColor: 'transparent',
                            borderWidth: 2
                        }, {
                            label: 'Forecast',
                            data: Array.from({length: 30}, (_, i) => i < 15 ? null : Math.floor(Math.random() * 20000) + 28000),
                            borderColor: '#8b5cf6',
                            backgroundColor: 'rgba(139, 92, 246, 0.1)',
                            borderDash: [5, 5],
                            borderWidth: 2
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false
                    }
                });
            }
        });
    </script>
</body>
</html>