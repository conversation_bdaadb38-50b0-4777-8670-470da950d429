<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Data Refinery Platform - Data Sources</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/feather-icons/4.29.0/feather.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        :root {
            --bg-cream: #EAE4D5;
            --teal-primary: #14B8A6;
            --teal-dark: #0D9488;
            --teal-light: #5EEAD4;
            --teal-50: #F0FDFA;
            --gray-800: #1F2937;
            --gray-600: #4B5563;
            --gray-400: #9CA3AF;
            --success: #10B981;
            --warning: #F59E0B;
            --error: #EF4444;
            --info: #3B82F6;
        }
        
        body {
            background-color: var(--bg-cream);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        
        .glassmorphism {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.4);
            box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.08);
        }
        
        .glassmorphism-dark {
            background: rgba(31, 41, 55, 0.9);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            color: white;
        }
        
        .teal-gradient {
            background: linear-gradient(135deg, var(--teal-primary) 0%, var(--teal-dark) 100%);
        }
        
        .hover-lift {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        .hover-lift:hover {
            transform: translateY(-4px) scale(1.02);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        
        /* Animations */
        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        
        @keyframes pulse {
            0%, 100% { 
                opacity: 1;
                transform: scale(1);
            }
            50% { 
                opacity: 0.7;
                transform: scale(0.95);
            }
        }
        
        .animate-slide-in {
            animation: slideIn 0.5s ease-out;
        }
        
        .animate-fade-in {
            animation: fadeIn 0.3s ease-out;
        }
        
        .pulse-animation {
            animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
        }
        
        /* Status indicators */
        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            position: relative;
        }
        
        .status-dot::after {
            content: '';
            position: absolute;
            top: -4px;
            left: -4px;
            right: -4px;
            bottom: -4px;
            border-radius: 50%;
            background: inherit;
            opacity: 0.3;
            animation: pulse 2s infinite;
        }
        
        /* Progress rings */
        .progress-ring {
            transform: rotate(-90deg);
        }
        
        .progress-ring-circle {
            transition: stroke-dashoffset 0.5s ease;
        }
        
        /* Connection animation */
        @keyframes connect {
            0% { stroke-dashoffset: 100; }
            100% { stroke-dashoffset: 0; }
        }
        
        .connection-line {
            stroke-dasharray: 100;
            animation: connect 1s ease-out forwards;
        }
        
        /* Card hover effects */
        .source-card {
            position: relative;
            overflow: hidden;
        }
        
        .source-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }
        
        .source-card:hover::before {
            left: 100%;
        }
        
        /* Toggle switch */
        .toggle-switch {
            position: relative;
            width: 44px;
            height: 24px;
            background: var(--gray-400);
            border-radius: 12px;
            cursor: pointer;
            transition: background 0.3s;
        }
        
        .toggle-switch.active {
            background: var(--teal-primary);
        }
        
        .toggle-switch::after {
            content: '';
            position: absolute;
            top: 2px;
            left: 2px;
            width: 20px;
            height: 20px;
            background: white;
            border-radius: 50%;
            transition: transform 0.3s;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }
        
        .toggle-switch.active::after {
            transform: translateX(20px);
        }
        
        /* Search highlight */
        .search-highlight {
            background: rgba(20, 184, 166, 0.2);
            padding: 2px 4px;
            border-radius: 2px;
        }
        
        /* Loading skeleton */
        .skeleton {
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: loading 1.5s infinite;
        }
        
        @keyframes loading {
            0% { background-position: 200% 0; }
            100% { background-position: -200% 0; }
        }
        
        /* Custom scrollbar */
        ::-webkit-scrollbar {
            width: 10px;
            height: 10px;
        }
        
        ::-webkit-scrollbar-track {
            background: rgba(0, 0, 0, 0.05);
            border-radius: 5px;
        }
        
        ::-webkit-scrollbar-thumb {
            background: var(--teal-primary);
            border-radius: 5px;
        }
        
        ::-webkit-scrollbar-thumb:hover {
            background: var(--teal-dark);
        }
        
        /* File upload area */
        .upload-area {
            border: 2px dashed var(--gray-400);
            transition: all 0.3s;
        }
        
        .upload-area.dragover {
            border-color: var(--teal-primary);
            background: rgba(20, 184, 166, 0.05);
        }
    </style>
</head>
<body class="min-h-screen">
    <!-- Navigation Header -->
    <nav class="fixed top-0 left-0 right-0 z-50 glassmorphism">
        <div class="container mx-auto px-6 py-3">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-8">
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 teal-gradient rounded-lg flex items-center justify-center shadow-lg">
                            <i data-feather="database" class="w-6 h-6 text-white"></i>
                        </div>
                        <h1 class="text-2xl font-bold text-gray-800">DataRefinery</h1>
                    </div>
                    <div class="hidden lg:flex items-center space-x-6">
                        <a href="#" class="text-gray-600 hover:text-teal-600 font-medium transition-colors">Dashboard</a>
                        <a href="#" class="text-teal-600 font-medium">Data Sources</a>
                        <a href="#" class="text-gray-600 hover:text-teal-600 font-medium transition-colors">Pipelines</a>
                        <a href="#" class="text-gray-600 hover:text-teal-600 font-medium transition-colors">Monitoring</a>
                        <a href="#" class="text-gray-600 hover:text-teal-600 font-medium transition-colors">Data Quality</a>
                        <a href="#" class="text-gray-600 hover:text-teal-600 font-medium transition-colors">Analytics</a>
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <button class="p-2 rounded-lg hover:bg-gray-100 transition-colors">
                        <i data-feather="bell" class="w-5 h-5 text-gray-600"></i>
                    </button>
                    <div class="flex items-center space-x-3 pl-4 border-l border-gray-200">
                        <div class="hidden md:text-right">
                            <p class="text-sm font-medium text-gray-800">John Doe</p>
                            <p class="text-xs text-gray-500">Premium Plan</p>
                        </div>
                        <div class="w-10 h-10 bg-teal-100 rounded-full flex items-center justify-center">
                            <span class="text-teal-600 font-semibold">JD</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="pt-20 pb-8">
        <div class="container mx-auto px-6 py-8">
            <!-- Page Header -->
            <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-8">
                <div class="mb-4 lg:mb-0">
                    <h2 class="text-3xl font-bold text-gray-800 mb-2">Data Sources</h2>
                    <p class="text-gray-600">Connect, configure, and manage all your data integrations in one place</p>
                </div>
                <div class="flex flex-wrap gap-3">
                    <div class="relative">
                        <input type="text" 
                               placeholder="Search sources..." 
                               class="w-64 px-4 py-2 pl-10 bg-white border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-teal-500"
                               id="sourceSearch">
                        <i data-feather="search" class="w-5 h-5 text-gray-400 absolute left-3 top-2.5"></i>
                    </div>
                    <button class="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg font-medium hover:bg-gray-200 transition-colors flex items-center space-x-2">
                        <i data-feather="filter" class="w-4 h-4"></i>
                        <span>Filter</span>
                    </button>
                    <button onclick="openAddSourceModal()" class="px-4 py-2 teal-gradient text-white rounded-lg font-medium hover:opacity-90 transition-opacity flex items-center space-x-2">
                        <i data-feather="plus" class="w-4 h-4"></i>
                        <span>Add Data Source</span>
                    </button>
                </div>
            </div>

            <!-- Quick Stats -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4 mb-8">
                <div class="glassmorphism rounded-lg p-4 animate-slide-in">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-2xl font-bold text-gray-800">18</p>
                            <p class="text-sm text-gray-600">Total Sources</p>
                        </div>
                        <div class="w-12 h-12 bg-teal-100 rounded-lg flex items-center justify-center">
                            <i data-feather="database" class="w-6 h-6 text-teal-600"></i>
                        </div>
                    </div>
                </div>
                <div class="glassmorphism rounded-lg p-4 animate-slide-in" style="animation-delay: 0.1s">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-2xl font-bold text-gray-800">14</p>
                            <p class="text-sm text-gray-600">Connected</p>
                        </div>
                        <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                            <i data-feather="check-circle" class="w-6 h-6 text-green-600"></i>
                        </div>
                    </div>
                </div>
                <div class="glassmorphism rounded-lg p-4 animate-slide-in" style="animation-delay: 0.2s">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-2xl font-bold text-gray-800">2</p>
                            <p class="text-sm text-gray-600">Syncing</p>
                        </div>
                        <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                            <i data-feather="refresh-cw" class="w-6 h-6 text-blue-600 animate-spin"></i>
                        </div>
                    </div>
                </div>
                <div class="glassmorphism rounded-lg p-4 animate-slide-in" style="animation-delay: 0.3s">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-2xl font-bold text-gray-800">2</p>
                            <p class="text-sm text-gray-600">Errors</p>
                        </div>
                        <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                            <i data-feather="alert-circle" class="w-6 h-6 text-red-600"></i>
                        </div>
                    </div>
                </div>
                <div class="glassmorphism rounded-lg p-4 animate-slide-in" style="animation-delay: 0.4s">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-2xl font-bold text-gray-800">3.2TB</p>
                            <p class="text-sm text-gray-600">Data Volume</p>
                        </div>
                        <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                            <i data-feather="hard-drive" class="w-6 h-6 text-purple-600"></i>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Source Categories -->
            <div class="mb-8">
                <div class="flex items-center space-x-2 overflow-x-auto pb-2">
                    <button class="px-4 py-2 bg-teal-100 text-teal-700 rounded-lg font-medium whitespace-nowrap transition-colors">
                        <span class="flex items-center space-x-2">
                            <i data-feather="grid" class="w-4 h-4"></i>
                            <span>All Sources (18)</span>
                        </span>
                    </button>
                    <button class="px-4 py-2 text-gray-600 hover:bg-gray-100 rounded-lg font-medium whitespace-nowrap transition-colors">
                        <span class="flex items-center space-x-2">
                            <i data-feather="shopping-cart" class="w-4 h-4"></i>
                            <span>E-commerce (4)</span>
                        </span>
                    </button>
                    <button class="px-4 py-2 text-gray-600 hover:bg-gray-100 rounded-lg font-medium whitespace-nowrap transition-colors">
                        <span class="flex items-center space-x-2">
                            <i data-feather="bar-chart" class="w-4 h-4"></i>
                            <span>Analytics (3)</span>
                        </span>
                    </button>
                    <button class="px-4 py-2 text-gray-600 hover:bg-gray-100 rounded-lg font-medium whitespace-nowrap transition-colors">
                        <span class="flex items-center space-x-2">
                            <i data-feather="users" class="w-4 h-4"></i>
                            <span>CRM (2)</span>
                        </span>
                    </button>
                    <button class="px-4 py-2 text-gray-600 hover:bg-gray-100 rounded-lg font-medium whitespace-nowrap transition-colors">
                        <span class="flex items-center space-x-2">
                            <i data-feather="mail" class="w-4 h-4"></i>
                            <span>Marketing (3)</span>
                        </span>
                    </button>
                    <button class="px-4 py-2 text-gray-600 hover:bg-gray-100 rounded-lg font-medium whitespace-nowrap transition-colors">
                        <span class="flex items-center space-x-2">
                            <i data-feather="server" class="w-4 h-4"></i>
                            <span>Databases (4)</span>
                        </span>
                    </button>
                    <button class="px-4 py-2 text-gray-600 hover:bg-gray-100 rounded-lg font-medium whitespace-nowrap transition-colors">
                        <span class="flex items-center space-x-2">
                            <i data-feather="upload" class="w-4 h-4"></i>
                            <span>File Uploads (2)</span>
                        </span>
                    </button>
                </div>
            </div>

            <!-- Connected Sources Grid -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
                <!-- Shopify Source (Connected) -->
                <div class="glassmorphism rounded-xl hover-lift source-card animate-fade-in">
                    <div class="p-6">
                        <div class="flex items-start justify-between mb-4">
                            <div class="flex items-center space-x-3">
                                <div class="w-14 h-14 bg-gradient-to-br from-green-100 to-green-50 rounded-xl flex items-center justify-center">
                                    <i data-feather="shopping-cart" class="w-7 h-7 text-green-600"></i>
                                </div>
                                <div>
                                    <h3 class="font-semibold text-gray-800">Shopify Store</h3>
                                    <p class="text-sm text-gray-500">E-commerce Platform</p>
                                </div>
                            </div>
                            <div class="flex items-center space-x-1">
                                <div class="status-dot bg-green-500"></div>
                                <span class="text-xs text-green-600 font-medium">Connected</span>
                            </div>
                        </div>
                        
                        <div class="space-y-3 mb-4">
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600">Last Sync</span>
                                <span class="text-sm font-medium text-gray-800">5 mins ago</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600">Records</span>
                                <span class="text-sm font-medium text-gray-800">245,320</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600">Data Volume</span>
                                <span class="text-sm font-medium text-gray-800">124.5 GB</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600">Sync Frequency</span>
                                <div class="flex items-center space-x-2">
                                    <span class="text-sm font-medium text-gray-800">Real-time</span>
                                    <div class="w-2 h-2 bg-green-500 rounded-full pulse-animation"></div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-4">
                            <div class="flex items-center justify-between text-sm mb-1">
                                <span class="text-gray-600">Sync Progress</span>
                                <span class="font-medium text-gray-800">100%</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div class="bg-gradient-to-r from-green-500 to-green-600 h-2 rounded-full" style="width: 100%"></div>
                            </div>
                        </div>
                        
                        <div class="flex items-center space-x-2">
                            <button class="flex-1 px-3 py-2 bg-teal-50 text-teal-700 rounded-lg text-sm font-medium hover:bg-teal-100 transition-colors">
                                <i data-feather="settings" class="w-4 h-4 inline mr-1"></i>
                                Configure
                            </button>
                            <button class="flex-1 px-3 py-2 bg-gray-50 text-gray-700 rounded-lg text-sm font-medium hover:bg-gray-100 transition-colors">
                                <i data-feather="eye" class="w-4 h-4 inline mr-1"></i>
                                View Details
                            </button>
                        </div>
                    </div>
                    <div class="px-6 py-3 bg-gray-50 border-t border-gray-100 rounded-b-xl">
                        <div class="flex items-center justify-between text-xs">
                            <span class="text-gray-500">API Version: 2023-10</span>
                            <a href="#" class="text-teal-600 hover:text-teal-700 font-medium">View Logs →</a>
                        </div>
                    </div>
                </div>

                <!-- Google Analytics (Syncing) -->
                <div class="glassmorphism rounded-xl hover-lift source-card animate-fade-in" style="animation-delay: 0.1s">
                    <div class="p-6">
                        <div class="flex items-start justify-between mb-4">
                            <div class="flex items-center space-x-3">
                                <div class="w-14 h-14 bg-gradient-to-br from-blue-100 to-blue-50 rounded-xl flex items-center justify-center">
                                    <i data-feather="bar-chart-2" class="w-7 h-7 text-blue-600"></i>
                                </div>
                                <div>
                                    <h3 class="font-semibold text-gray-800">Google Analytics 4</h3>
                                    <p class="text-sm text-gray-500">Web Analytics</p>
                                </div>
                            </div>
                            <div class="flex items-center space-x-1">
                                <div class="status-dot bg-blue-500 pulse-animation"></div>
                                <span class="text-xs text-blue-600 font-medium">Syncing</span>
                            </div>
                        </div>
                        
                        <div class="space-y-3 mb-4">
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600">Last Sync</span>
                                <span class="text-sm font-medium text-gray-800">In Progress</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600">Records</span>
                                <span class="text-sm font-medium text-gray-800">1.2M+</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600">Data Volume</span>
                                <span class="text-sm font-medium text-gray-800">256.8 GB</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600">Sync Frequency</span>
                                <span class="text-sm font-medium text-gray-800">Hourly</span>
                            </div>
                        </div>
                        
                        <div class="mb-4">
                            <div class="flex items-center justify-between text-sm mb-1">
                                <span class="text-gray-600">Sync Progress</span>
                                <span class="font-medium text-gray-800">67%</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2 overflow-hidden">
                                <div class="bg-gradient-to-r from-blue-500 to-blue-600 h-2 rounded-full transition-all duration-500" style="width: 67%"></div>
                            </div>
                            <p class="text-xs text-gray-500 mt-1">ETA: 12 minutes</p>
                        </div>
                        
                        <div class="flex items-center space-x-2">
                            <button class="flex-1 px-3 py-2 bg-blue-50 text-blue-700 rounded-lg text-sm font-medium hover:bg-blue-100 transition-colors">
                                <i data-feather="pause" class="w-4 h-4 inline mr-1"></i>
                                Pause Sync
                            </button>
                            <button class="flex-1 px-3 py-2 bg-gray-50 text-gray-700 rounded-lg text-sm font-medium hover:bg-gray-100 transition-colors">
                                <i data-feather="eye" class="w-4 h-4 inline mr-1"></i>
                                View Details
                            </button>
                        </div>
                    </div>
                    <div class="px-6 py-3 bg-gray-50 border-t border-gray-100 rounded-b-xl">
                        <div class="flex items-center justify-between text-xs">
                            <span class="text-gray-500">Property ID: GA4-123456</span>
                            <a href="#" class="text-teal-600 hover:text-teal-700 font-medium">View Logs →</a>
                        </div>
                    </div>
                </div>

                <!-- Stripe (Error) -->
                <div class="glassmorphism rounded-xl hover-lift source-card animate-fade-in border-2 border-red-200" style="animation-delay: 0.2s">
                    <div class="p-6">
                        <div class="flex items-start justify-between mb-4">
                            <div class="flex items-center space-x-3">
                                <div class="w-14 h-14 bg-gradient-to-br from-purple-100 to-purple-50 rounded-xl flex items-center justify-center">
                                    <i data-feather="credit-card" class="w-7 h-7 text-purple-600"></i>
                                </div>
                                <div>
                                    <h3 class="font-semibold text-gray-800">Stripe Payments</h3>
                                    <p class="text-sm text-gray-500">Payment Gateway</p>
                                </div>
                            </div>
                            <div class="flex items-center space-x-1">
                                <div class="status-dot bg-red-500"></div>
                                <span class="text-xs text-red-600 font-medium">Error</span>
                            </div>
                        </div>
                        
                        <div class="bg-red-50 border border-red-200 rounded-lg p-3 mb-4">
                            <div class="flex items-start space-x-2">
                                <i data-feather="alert-circle" class="w-4 h-4 text-red-600 mt-0.5"></i>
                                <div class="flex-1">
                                    <p class="text-sm font-medium text-red-800">Authentication Failed</p>
                                    <p class="text-xs text-red-600 mt-1">API key expired. Please update your credentials.</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="space-y-3 mb-4">
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600">Last Successful Sync</span>
                                <span class="text-sm font-medium text-gray-800">2 hours ago</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600">Records</span>
                                <span class="text-sm font-medium text-gray-800">89,450</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600">Failed Attempts</span>
                                <span class="text-sm font-medium text-red-600">3</span>
                            </div>
                        </div>
                        
                        <div class="flex items-center space-x-2">
                            <button class="flex-1 px-3 py-2 bg-red-50 text-red-700 rounded-lg text-sm font-medium hover:bg-red-100 transition-colors">
                                <i data-feather="key" class="w-4 h-4 inline mr-1"></i>
                                Update Credentials
                            </button>
                            <button class="flex-1 px-3 py-2 bg-gray-50 text-gray-700 rounded-lg text-sm font-medium hover:bg-gray-100 transition-colors">
                                <i data-feather="refresh-cw" class="w-4 h-4 inline mr-1"></i>
                                Retry
                            </button>
                        </div>
                    </div>
                    <div class="px-6 py-3 bg-red-50 border-t border-red-100 rounded-b-xl">
                        <div class="flex items-center justify-between text-xs">
                            <span class="text-red-600">Error Code: AUTH_401</span>
                            <a href="#" class="text-red-600 hover:text-red-700 font-medium">View Error Logs →</a>
                        </div>
                    </div>
                </div>

                <!-- PostgreSQL Database -->
                <div class="glassmorphism rounded-xl hover-lift source-card animate-fade-in" style="animation-delay: 0.3s">
                    <div class="p-6">
                        <div class="flex items-start justify-between mb-4">
                            <div class="flex items-center space-x-3">
                                <div class="w-14 h-14 bg-gradient-to-br from-indigo-100 to-indigo-50 rounded-xl flex items-center justify-center">
                                    <i data-feather="database" class="w-7 h-7 text-indigo-600"></i>
                                </div>
                                <div>
                                    <h3 class="font-semibold text-gray-800">PostgreSQL Production</h3>
                                    <p class="text-sm text-gray-500">Database</p>
                                </div>
                            </div>
                            <div class="flex items-center space-x-1">
                                <div class="status-dot bg-green-500"></div>
                                <span class="text-xs text-green-600 font-medium">Connected</span>
                            </div>
                        </div>
                        
                        <div class="space-y-3 mb-4">
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600">Last Sync</span>
                                <span class="text-sm font-medium text-gray-800">1 hour ago</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600">Tables</span>
                                <span class="text-sm font-medium text-gray-800">47</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600">Total Rows</span>
                                <span class="text-sm font-medium text-gray-800">3.2M</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600">Sync Method</span>
                                <span class="text-sm font-medium text-gray-800">CDC</span>
                            </div>
                        </div>
                        
                        <div class="mb-4">
                            <p class="text-xs text-gray-600 mb-2">Database Health</p>
                            <div class="grid grid-cols-3 gap-2">
                                <div class="text-center">
                                    <div class="text-sm font-semibold text-green-600">98%</div>
                                    <div class="text-xs text-gray-500">Uptime</div>
                                </div>
                                <div class="text-center">
                                    <div class="text-sm font-semibold text-gray-800">42ms</div>
                                    <div class="text-xs text-gray-500">Latency</div>
                                </div>
                                <div class="text-center">
                                    <div class="text-sm font-semibold text-gray-800">15</div>
                                    <div class="text-xs text-gray-500">Connections</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="flex items-center space-x-2">
                            <button class="flex-1 px-3 py-2 bg-teal-50 text-teal-700 rounded-lg text-sm font-medium hover:bg-teal-100 transition-colors">
                                <i data-feather="settings" class="w-4 h-4 inline mr-1"></i>
                                Configure
                            </button>
                            <button class="flex-1 px-3 py-2 bg-gray-50 text-gray-700 rounded-lg text-sm font-medium hover:bg-gray-100 transition-colors">
                                <i data-feather="terminal" class="w-4 h-4 inline mr-1"></i>
                                Query
                            </button>
                        </div>
                    </div>
                    <div class="px-6 py-3 bg-gray-50 border-t border-gray-100 rounded-b-xl">
                        <div class="flex items-center justify-between text-xs">
                            <span class="text-gray-500">Host: db.production.aws</span>
                            <a href="#" class="text-teal-600 hover:text-teal-700 font-medium">Schema Explorer →</a>
                        </div>
                    </div>
                </div>

                <!-- File Upload Source -->
                <div class="glassmorphism rounded-xl hover-lift source-card animate-fade-in" style="animation-delay: 0.4s">
                    <div class="p-6">
                        <div class="flex items-start justify-between mb-4">
                            <div class="flex items-center space-x-3">
                                <div class="w-14 h-14 bg-gradient-to-br from-orange-100 to-orange-50 rounded-xl flex items-center justify-center">
                                    <i data-feather="upload-cloud" class="w-7 h-7 text-orange-600"></i>
                                </div>
                                <div>
                                    <h3 class="font-semibold text-gray-800">Monthly Reports</h3>
                                    <p class="text-sm text-gray-500">File Upload</p>
                                </div>
                            </div>
                            <div class="flex items-center space-x-1">
                                <div class="status-dot bg-green-500"></div>
                                <span class="text-xs text-green-600 font-medium">Active</span>
                            </div>
                        </div>
                        
                        <div class="upload-area rounded-lg p-4 mb-4 text-center cursor-pointer hover:bg-gray-50 transition-colors"
                             ondragover="event.preventDefault(); this.classList.add('dragover')"
                             ondragleave="this.classList.remove('dragover')"
                             ondrop="event.preventDefault(); this.classList.remove('dragover')">
                            <i data-feather="upload" class="w-8 h-8 text-gray-400 mx-auto mb-2"></i>
                            <p class="text-sm text-gray-600">Drop files here or click to upload</p>
                            <p class="text-xs text-gray-500 mt-1">CSV, Excel, JSON (Max 50MB)</p>
                        </div>
                        
                        <div class="space-y-2 mb-4">
                            <div class="flex items-center justify-between text-sm">
                                <span class="text-gray-600">Last Upload</span>
                                <span class="font-medium text-gray-800">Yesterday, 2:30 PM</span>
                            </div>
                            <div class="flex items-center justify-between text-sm">
                                <span class="text-gray-600">Total Files</span>
                                <span class="font-medium text-gray-800">24</span>
                            </div>
                            <div class="flex items-center justify-between text-sm">
                                <span class="text-gray-600">Schedule</span>
                                <span class="font-medium text-gray-800">Monthly</span>
                            </div>
                        </div>
                        
                        <div class="flex items-center space-x-2">
                            <button class="flex-1 px-3 py-2 bg-orange-50 text-orange-700 rounded-lg text-sm font-medium hover:bg-orange-100 transition-colors">
                                <i data-feather="upload" class="w-4 h-4 inline mr-1"></i>
                                Upload Now
                            </button>
                            <button class="flex-1 px-3 py-2 bg-gray-50 text-gray-700 rounded-lg text-sm font-medium hover:bg-gray-100 transition-colors">
                                <i data-feather="file-text" class="w-4 h-4 inline mr-1"></i>
                                View Files
                            </button>
                        </div>
                    </div>
                    <div class="px-6 py-3 bg-gray-50 border-t border-gray-100 rounded-b-xl">
                        <div class="flex items-center justify-between text-xs">
                            <span class="text-gray-500">Next upload: Feb 1, 2025</span>
                            <a href="#" class="text-teal-600 hover:text-teal-700 font-medium">Upload History →</a>
                        </div>
                    </div>
                </div>

                <!-- Add New Source Card -->
                <div class="border-2 border-dashed border-gray-300 rounded-xl p-6 flex flex-col items-center justify-center hover:border-teal-400 cursor-pointer transition-all hover:bg-gray-50 min-h-[400px] animate-fade-in" 
                     style="animation-delay: 0.5s"
                     onclick="openAddSourceModal()">
                    <div class="w-20 h-20 bg-gray-100 rounded-full flex items-center justify-center mb-4">
                        <i data-feather="plus" class="w-10 h-10 text-gray-400"></i>
                    </div>
                    <h3 class="font-semibold text-gray-700 mb-2">Add New Data Source</h3>
                    <p class="text-sm text-gray-500 text-center max-w-xs">Connect a new integration to start syncing data to your platform</p>
                    <button class="mt-4 px-4 py-2 bg-teal-50 text-teal-700 rounded-lg text-sm font-medium hover:bg-teal-100 transition-colors">
                        Browse Integrations
                    </button>
                </div>
            </div>

            <!-- Disconnected Sources Section -->
            <div class="mb-8">
                <h3 class="text-lg font-semibold text-gray-800 mb-4">Disconnected Sources</h3>
                <div class="glassmorphism rounded-xl p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        <div class="border border-gray-200 rounded-lg p-4 opacity-75">
                            <div class="flex items-center justify-between mb-3">
                                <div class="flex items-center space-x-3">
                                    <div class="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center">
                                        <i data-feather="message-circle" class="w-5 h-5 text-gray-400"></i>
                                    </div>
                                    <div>
                                        <h4 class="font-medium text-gray-700">Zendesk Support</h4>
                                        <p class="text-xs text-gray-500">Disconnected 5 days ago</p>
                                    </div>
                                </div>
                                <button class="text-teal-600 text-sm font-medium hover:text-teal-700">Reconnect</button>
                            </div>
                        </div>
                        
                        <div class="border border-gray-200 rounded-lg p-4 opacity-75">
                            <div class="flex items-center justify-between mb-3">
                                <div class="flex items-center space-x-3">
                                    <div class="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center">
                                        <i data-feather="box" class="w-5 h-5 text-gray-400"></i>
                                    </div>
                                    <div>
                                        <h4 class="font-medium text-gray-700">MongoDB Atlas</h4>
                                        <p class="text-xs text-gray-500">Never connected</p>
                                    </div>
                                </div>
                                <button class="text-teal-600 text-sm font-medium hover:text-teal-700">Connect</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Available Integrations -->
            <div>
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-gray-800">Popular Integrations</h3>
                    <a href="#" class="text-sm text-teal-600 font-medium hover:text-teal-700">View All 150+ Integrations →</a>
                </div>
                <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
                    <button class="glassmorphism rounded-lg p-4 hover:bg-gray-50 transition-colors group">
                        <div class="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center mx-auto mb-2 group-hover:bg-teal-100 transition-colors">
                            <i data-feather="users" class="w-6 h-6 text-gray-600 group-hover:text-teal-600"></i>
                        </div>
                        <p class="text-sm font-medium text-gray-700">Salesforce</p>
                    </button>
                    <button class="glassmorphism rounded-lg p-4 hover:bg-gray-50 transition-colors group">
                        <div class="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center mx-auto mb-2 group-hover:bg-teal-100 transition-colors">
                            <i data-feather="trello" class="w-6 h-6 text-gray-600 group-hover:text-teal-600"></i>
                        </div>
                        <p class="text-sm font-medium text-gray-700">HubSpot</p>
                    </button>
                    <button class="glassmorphism rounded-lg p-4 hover:bg-gray-50 transition-colors group">
                        <div class="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center mx-auto mb-2 group-hover:bg-teal-100 transition-colors">
                        <i data-feather="cloud" class="w-6 h-6 text-gray-600 group-hover:text-teal-600"></i>
                        </div>
                        <p class="text-sm font-medium text-gray-700">AWS S3</p>
                    </button>
                    <button class="glassmorphism rounded-lg p-4 hover:bg-gray-50 transition-colors group">
                        <div class="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center mx-auto mb-2 group-hover:bg-teal-100 transition-colors">
                            <i data-feather="facebook" class="w-6 h-6 text-gray-600 group-hover:text-teal-600"></i>
                        </div>
                        <p class="text-sm font-medium text-gray-700">Facebook Ads</p>
                    </button>
                    <button class="glassmorphism rounded-lg p-4 hover:bg-gray-50 transition-colors group">
                        <div class="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center mx-auto mb-2 group-hover:bg-teal-100 transition-colors">
                            <i data-feather="twitter" class="w-6 h-6 text-gray-600 group-hover:text-teal-600"></i>
                        </div>
                        <p class="text-sm font-medium text-gray-700">Twitter Ads</p>
                    </button>
                    <button class="glassmorphism rounded-lg p-4 hover:bg-gray-50 transition-colors group">
                        <div class="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center mx-auto mb-2 group-hover:bg-teal-100 transition-colors">
                            <i data-feather="slack" class="w-6 h-6 text-gray-600 group-hover:text-teal-600"></i>
                        </div>
                        <p class="text-sm font-medium text-gray-700">Slack</p>
                    </button>
                </div>
            </div>
        </div>
    </main>

    <!-- Add Source Modal (Hidden by default) -->
    <div id="addSourceModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-center justify-center p-6">
        <div class="glassmorphism rounded-xl max-w-4xl w-full max-h-[90vh] overflow-hidden animate-slide-in">
            <div class="p-6 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h2 class="text-2xl font-bold text-gray-800">Add New Data Source</h2>
                    <button onclick="closeAddSourceModal()" class="text-gray-400 hover:text-gray-600">
                        <i data-feather="x" class="w-6 h-6"></i>
                    </button>
                </div>
            </div>
            
            <div class="p-6 overflow-y-auto max-h-[calc(90vh-200px)]">
                <!-- Search -->
                <div class="mb-6">
                    <div class="relative">
                        <input type="text" 
                               placeholder="Search integrations..." 
                               class="w-full px-4 py-3 pl-10 bg-gray-50 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-teal-500">
                        <i data-feather="search" class="w-5 h-5 text-gray-400 absolute left-3 top-3.5"></i>
                    </div>
                </div>
                
                <!-- Categories -->
                <div class="mb-6">
                    <h3 class="text-sm font-semibold text-gray-600 uppercase tracking-wider mb-3">Categories</h3>
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-3">
                        <button class="px-4 py-2 bg-teal-50 text-teal-700 rounded-lg text-sm font-medium hover:bg-teal-100 transition-colors">
                            All Sources
                        </button>
                        <button class="px-4 py-2 bg-gray-50 text-gray-700 rounded-lg text-sm font-medium hover:bg-gray-100 transition-colors">
                            E-commerce
                        </button>
                        <button class="px-4 py-2 bg-gray-50 text-gray-700 rounded-lg text-sm font-medium hover:bg-gray-100 transition-colors">
                            Analytics
                        </button>
                        <button class="px-4 py-2 bg-gray-50 text-gray-700 rounded-lg text-sm font-medium hover:bg-gray-100 transition-colors">
                            Databases
                        </button>
                    </div>
                </div>
                
                <!-- Integration Grid -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <!-- Integration options would go here -->
                    <div class="border border-gray-200 rounded-lg p-4 hover:border-teal-400 cursor-pointer transition-all">
                        <div class="flex items-start space-x-3">
                            <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                                <i data-feather="shopping-bag" class="w-6 h-6 text-green-600"></i>
                            </div>
                            <div class="flex-1">
                                <h4 class="font-semibold text-gray-800">WooCommerce</h4>
                                <p class="text-sm text-gray-600 mt-1">WordPress e-commerce platform</p>
                                <div class="flex items-center space-x-4 mt-2 text-xs text-gray-500">
                                    <span>Real-time sync</span>
                                    <span>•</span>
                                    <span>API v3</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Initialize Feather Icons
        feather.replace();

        // Modal functions
        function openAddSourceModal() {
            document.getElementById('addSourceModal').classList.remove('hidden');
            document.body.style.overflow = 'hidden';
        }

        function closeAddSourceModal() {
            document.getElementById('addSourceModal').classList.add('hidden');
            document.body.style.overflow = 'auto';
        }

        // Search functionality
        document.getElementById('sourceSearch').addEventListener('input', function(e) {
            const searchTerm = e.target.value.toLowerCase();
            // Implement search logic here
            console.log('Searching for:', searchTerm);
        });

        // Simulate real-time updates
        function updateSyncProgress() {
            const progressBars = document.querySelectorAll('.transition-all');
            progressBars.forEach(bar => {
                if (bar.style.width && bar.style.width !== '100%') {
                    const current = parseInt(bar.style.width);
                    const newWidth = Math.min(current + Math.random() * 5, 100);
                    bar.style.width = newWidth + '%';
                }
            });
        }

        // Update progress every 2 seconds
        setInterval(updateSyncProgress, 2000);

        // Click outside modal to close
        document.getElementById('addSourceModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeAddSourceModal();
            }
        });
    </script>
</body>
</html>