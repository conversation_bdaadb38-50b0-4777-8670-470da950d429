<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Data Refinery Platform - Pipeline Builder</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/feather-icons/4.29.0/feather.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/interact.js/1.10.17/interact.min.js"></script>
    <style>
        :root {
            --bg-cream: #EAE4D5;
            --teal-primary: #14B8A6;
            --teal-dark: #0D9488;
            --teal-light: #5EEAD4;
            --teal-50: #F0FDFA;
            --gray-800: #1F2937;
            --gray-600: #4B5563;
            --gray-400: #9CA3AF;
            --success: #10B981;
            --warning: #F59E0B;
            --error: #EF4444;
            --info: #3B82F6;
        }
        
        body {
            background-color: var(--bg-cream);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        
        .glassmorphism {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.4);
            box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.08);
        }
        
        .teal-gradient {
            background: linear-gradient(135deg, var(--teal-primary) 0%, var(--teal-dark) 100%);
        }
        
        /* Pipeline Canvas */
        .pipeline-canvas {
            background-image: 
                radial-gradient(circle, rgba(20, 184, 166, 0.1) 1px, transparent 1px),
                radial-gradient(circle, rgba(20, 184, 166, 0.1) 1px, transparent 1px);
            background-size: 20px 20px;
            background-position: 0 0, 10px 10px;
            min-height: 600px;
            position: relative;
        }
        
        /* Pipeline Nodes */
        .pipeline-node {
            background: white;
            border: 2px solid var(--teal-primary);
            border-radius: 12px;
            padding: 16px;
            min-width: 220px;
            position: absolute;
            cursor: move;
            transition: all 0.3s;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        
        .pipeline-node:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
        }
        
        .pipeline-node.dragging {
            opacity: 0.8;
            z-index: 100;
        }
        
        .pipeline-node.selected {
            border-color: var(--teal-dark);
            box-shadow: 0 0 0 3px rgba(20, 184, 166, 0.2);
        }
        
        /* Node Types */
        .node-source { border-left: 4px solid #3B82F6; }
        .node-transform { border-left: 4px solid #8B5CF6; }
        .node-filter { border-left: 4px solid #F59E0B; }
        .node-join { border-left: 4px solid #EC4899; }
        .node-aggregate { border-left: 4px solid #10B981; }
        .node-destination { border-left: 4px solid #14B8A6; }
        
        /* Connection Ports */
        .node-port {
            width: 12px;
            height: 12px;
            background: white;
            border: 2px solid var(--teal-primary);
            border-radius: 50%;
            position: absolute;
            cursor: crosshair;
            transition: all 0.2s;
        }
        
        .node-port:hover {
            transform: scale(1.3);
            background: var(--teal-primary);
        }
        
        .node-port-input {
            left: -6px;
            top: 50%;
            transform: translateY(-50%);
        }
        
        .node-port-output {
            right: -6px;
            top: 50%;
            transform: translateY(-50%);
        }
        
        /* Connection Lines */
        .pipeline-connection {
            stroke: var(--teal-primary);
            stroke-width: 2;
            fill: none;
            pointer-events: none;
        }
        
        .pipeline-connection.animated {
            stroke-dasharray: 5;
            animation: flow 1s linear infinite;
        }
        
        @keyframes flow {
            0% { stroke-dashoffset: 0; }
            100% { stroke-dashoffset: -10; }
        }
        
        /* Sidebar */
        .component-item {
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 12px;
            cursor: grab;
            transition: all 0.2s;
        }
        
        .component-item:hover {
            border-color: var(--teal-primary);
            transform: translateX(4px);
        }
        
        .component-item:active {
            cursor: grabbing;
        }
        
        /* Properties Panel */
        .properties-panel {
            position: absolute;
            right: 20px;
            top: 20px;
            width: 320px;
            max-height: 80vh;
            overflow-y: auto;
            transition: transform 0.3s;
        }
        
        .properties-panel.hidden {
            transform: translateX(400px);
        }
        
        /* Code Editor */
        .code-editor {
            font-family: 'Monaco', 'Consolas', 'Courier New', monospace;
            font-size: 13px;
            line-height: 1.5;
            background: #1e293b;
            color: #e2e8f0;
            padding: 16px;
            border-radius: 8px;
            overflow-x: auto;
        }
        
        /* Animations */
        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        
        .animate-slide-in {
            animation: slideIn 0.5s ease-out;
        }
        
        .animate-fade-in {
            animation: fadeIn 0.3s ease-out;
        }
        
        /* Toggle Switch */
        .toggle-switch {
            position: relative;
            width: 44px;
            height: 24px;
            background: var(--gray-400);
            border-radius: 12px;
            cursor: pointer;
            transition: background 0.3s;
        }
        
        .toggle-switch.active {
            background: var(--teal-primary);
        }
        
        .toggle-switch::after {
            content: '';
            position: absolute;
            top: 2px;
            left: 2px;
            width: 20px;
            height: 20px;
            background: white;
            border-radius: 50%;
            transition: transform 0.3s;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }
        
        .toggle-switch.active::after {
            transform: translateX(20px);
        }
        
        /* Tabs */
        .tab-button {
            padding: 8px 16px;
            border-bottom: 2px solid transparent;
            transition: all 0.2s;
        }
        
        .tab-button.active {
            border-bottom-color: var(--teal-primary);
            color: var(--teal-primary);
        }
        
        /* Status Badge */
        .status-badge {
            display: inline-flex;
            align-items: center;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .status-badge.draft { background: #f3f4f6; color: #6b7280; }
        .status-badge.testing { background: #fef3c7; color: #d97706; }
        .status-badge.deployed { background: #d1fae5; color: #059669; }
        .status-badge.error { background: #fee2e2; color: #dc2626; }
    </style>
</head>
<body class="min-h-screen">
    <!-- Navigation Header -->
    <nav class="fixed top-0 left-0 right-0 z-50 glassmorphism">
        <div class="container mx-auto px-6 py-3">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-8">
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 teal-gradient rounded-lg flex items-center justify-center shadow-lg">
                            <i data-feather="database" class="w-6 h-6 text-white"></i>
                        </div>
                        <h1 class="text-2xl font-bold text-gray-800">DataRefinery</h1>
                    </div>
                    <div class="hidden lg:flex items-center space-x-6">
                        <a href="#" class="text-gray-600 hover:text-teal-600 font-medium transition-colors">Dashboard</a>
                        <a href="#" class="text-gray-600 hover:text-teal-600 font-medium transition-colors">Data Sources</a>
                        <a href="#" class="text-teal-600 font-medium">Pipelines</a>
                        <a href="#" class="text-gray-600 hover:text-teal-600 font-medium transition-colors">Monitoring</a>
                        <a href="#" class="text-gray-600 hover:text-teal-600 font-medium transition-colors">Data Quality</a>
                        <a href="#" class="text-gray-600 hover:text-teal-600 font-medium transition-colors">Analytics</a>
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <button class="p-2 rounded-lg hover:bg-gray-100 transition-colors">
                        <i data-feather="bell" class="w-5 h-5 text-gray-600"></i>
                    </button>
                    <div class="flex items-center space-x-3 pl-4 border-l border-gray-200">
                        <div class="w-10 h-10 bg-teal-100 rounded-full flex items-center justify-center">
                            <span class="text-teal-600 font-semibold">JD</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="pt-16 flex h-screen">
        <!-- Left Sidebar - Components -->
        <aside class="w-64 glassmorphism border-r border-gray-200 overflow-y-auto">
            <div class="p-4">
                <h3 class="text-sm font-semibold text-gray-600 uppercase tracking-wider mb-4">Pipeline Components</h3>
                
                <!-- Sources -->
                <div class="mb-6">
                    <h4 class="text-xs font-semibold text-gray-500 uppercase tracking-wider mb-2">Sources</h4>
                    <div class="space-y-2">
                        <div class="component-item" draggable="true" data-component-type="source" data-source-type="database">
                            <div class="flex items-center space-x-3">
                                <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                                    <i data-feather="database" class="w-4 h-4 text-blue-600"></i>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-800">Database</p>
                                    <p class="text-xs text-gray-500">SQL, NoSQL</p>
                                </div>
                            </div>
                        </div>
                        <div class="component-item" draggable="true" data-component-type="source" data-source-type="api">
                            <div class="flex items-center space-x-3">
                                <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                                    <i data-feather="globe" class="w-4 h-4 text-green-600"></i>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-800">API</p>
                                    <p class="text-xs text-gray-500">REST, GraphQL</p>
                                </div>
                            </div>
                        </div>
                        <div class="component-item" draggable="true" data-component-type="source" data-source-type="file">
                            <div class="flex items-center space-x-3">
                                <div class="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center">
                                    <i data-feather="file" class="w-4 h-4 text-orange-600"></i>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-800">File</p>
                                    <p class="text-xs text-gray-500">CSV, JSON, Excel</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Transformations -->
                <div class="mb-6">
                    <h4 class="text-xs font-semibold text-gray-500 uppercase tracking-wider mb-2">Transformations</h4>
                    <div class="space-y-2">
                        <div class="component-item" draggable="true" data-component-type="transform" data-transform-type="filter">
                            <div class="flex items-center space-x-3">
                                <div class="w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center">
                                    <i data-feather="filter" class="w-4 h-4 text-yellow-600"></i>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-800">Filter</p>
                                    <p class="text-xs text-gray-500">Row filtering</p>
                                </div>
                            </div>
                        </div>
                        <div class="component-item" draggable="true" data-component-type="transform" data-transform-type="map">
                            <div class="flex items-center space-x-3">
                                <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                                    <i data-feather="shuffle" class="w-4 h-4 text-purple-600"></i>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-800">Map</p>
                                    <p class="text-xs text-gray-500">Field mapping</p>
                                </div>
                            </div>
                        </div>
                        <div class="component-item" draggable="true" data-component-type="transform" data-transform-type="join">
                            <div class="flex items-center space-x-3">
                                <div class="w-8 h-8 bg-pink-100 rounded-lg flex items-center justify-center">
                                    <i data-feather="git-merge" class="w-4 h-4 text-pink-600"></i>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-800">Join</p>
                                    <p class="text-xs text-gray-500">Merge data</p>
                                </div>
                            </div>
                        </div>
                        <div class="component-item" draggable="true" data-component-type="transform" data-transform-type="aggregate">
                            <div class="flex items-center space-x-3">
                                <div class="w-8 h-8 bg-emerald-100 rounded-lg flex items-center justify-center">
                                    <i data-feather="layers" class="w-4 h-4 text-emerald-600"></i>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-800">Aggregate</p>
                                    <p class="text-xs text-gray-500">Group & sum</p>
                                </div>
                            </div>
                        </div>
                        <div class="component-item" draggable="true" data-component-type="transform" data-transform-type="custom">
                            <div class="flex items-center space-x-3">
                                <div class="w-8 h-8 bg-indigo-100 rounded-lg flex items-center justify-center">
                                    <i data-feather="code" class="w-4 h-4 text-indigo-600"></i>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-800">Custom</p>
                                    <p class="text-xs text-gray-500">Python, SQL</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Destinations -->
                <div class="mb-6">
                    <h4 class="text-xs font-semibold text-gray-500 uppercase tracking-wider mb-2">Destinations</h4>
                    <div class="space-y-2">
                        <div class="component-item" draggable="true" data-component-type="destination" data-destination-type="warehouse">
                            <div class="flex items-center space-x-3">
                                <div class="w-8 h-8 bg-teal-100 rounded-lg flex items-center justify-center">
                                    <i data-feather="hard-drive" class="w-4 h-4 text-teal-600"></i>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-800">Data Warehouse</p>
                                    <p class="text-xs text-gray-500">BigQuery, Snowflake</p>
                                </div>
                            </div>
                        </div>
                        <div class="component-item" draggable="true" data-component-type="destination" data-destination-type="database">
                            <div class="flex items-center space-x-3">
                                <div class="w-8 h-8 bg-cyan-100 rounded-lg flex items-center justify-center">
                                    <i data-feather="server" class="w-4 h-4 text-cyan-600"></i>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-800">Database</p>
                                    <p class="text-xs text-gray-500">PostgreSQL, MySQL</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </aside>

        <!-- Main Canvas Area -->
        <div class="flex-1 relative overflow-hidden">
            <!-- Top Toolbar -->
            <div class="absolute top-0 left-0 right-0 z-10 glassmorphism border-b border-gray-200">
                <div class="flex items-center justify-between px-6 py-3">
                    <div class="flex items-center space-x-6">
                        <div>
                            <h2 class="text-xl font-bold text-gray-800">Customer Data Pipeline</h2>
                            <div class="flex items-center space-x-2 mt-1">
                                <span class="status-badge draft">Draft</span>
                                <span class="text-xs text-gray-500">Last saved: 2 mins ago</span>
                            </div>
                        </div>
                        <div class="flex items-center space-x-2 pl-6 border-l border-gray-200">
                            <button class="p-2 rounded-lg hover:bg-gray-100 transition-colors" title="Undo">
                                <i data-feather="rotate-ccw" class="w-4 h-4 text-gray-600"></i>
                            </button>
                            <button class="p-2 rounded-lg hover:bg-gray-100 transition-colors" title="Redo">
                                <i data-feather="rotate-cw" class="w-4 h-4 text-gray-600"></i>
                            </button>
                        </div>
                    </div>
                    <div class="flex items-center space-x-3">
                        <button class="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg font-medium hover:bg-gray-200 transition-colors flex items-center space-x-2">
                            <i data-feather="save" class="w-4 h-4"></i>
                            <span>Save</span>
                        </button>
                        <button class="px-4 py-2 bg-blue-100 text-blue-700 rounded-lg font-medium hover:bg-blue-200 transition-colors flex items-center space-x-2">
                            <i data-feather="play" class="w-4 h-4"></i>
                            <span>Test Run</span>
                        </button>
                        <button class="px-4 py-2 teal-gradient text-white rounded-lg font-medium hover:opacity-90 transition-opacity flex items-center space-x-2">
                            <i data-feather="check-circle" class="w-4 h-4"></i>
                            <span>Deploy</span>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Pipeline Canvas -->
            <div class="pipeline-canvas mt-16 p-8" id="pipelineCanvas">
                <!-- SVG for connections -->
                <svg class="absolute inset-0 w-full h-full pointer-events-none">
                    <defs>
                        <marker id="arrowhead" markerWidth="10" markerHeight="10" refX="9" refY="3" orient="auto">
                            <polygon points="0 0, 10 3, 0 6" fill="#14B8A6" />
                        </marker>
                    </defs>
                    <!-- Connection lines will be drawn here -->
                    <path class="pipeline-connection animated" d="M 250 200 L 500 200" marker-end="url(#arrowhead)" />
                    <path class="pipeline-connection animated" d="M 500 200 L 750 200" marker-end="url(#arrowhead)" />
                </svg>

                <!-- Sample Pipeline Nodes -->
                <div class="pipeline-node node-source" style="left: 100px; top: 150px;" id="node1">
                    <div class="node-port node-port-output"></div>
                    <div class="flex items-center space-x-3 mb-3">
                        <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                            <i data-feather="database" class="w-5 h-5 text-blue-600"></i>
                        </div>
                        <div>
                            <h4 class="font-semibold text-gray-800">PostgreSQL</h4>
                            <p class="text-xs text-gray-500">orders_table</p>
                        </div>
                    </div>
                    <div class="text-xs text-gray-600 space-y-1">
                        <p>• Host: db.production.aws</p>
                        <p>• Table: orders</p>
                        <p>• Rows: ~45,000</p>
                    </div>
                </div>

                <div class="pipeline-node node-transform" style="left: 350px; top: 150px;" id="node2">
                    <div class="node-port node-port-input"></div>
                    <div class="node-port node-port-output"></div>
                    <div class="flex items-center space-x-3 mb-3">
                        <div class="w-10 h-10 bg-yellow-100 rounded-lg flex items-center justify-center">
                            <i data-feather="filter" class="w-5 h-5 text-yellow-600"></i>
                        </div>
                        <div>
                            <h4 class="font-semibold text-gray-800">Filter</h4>
                            <p class="text-xs text-gray-500">Remove test orders</p>
                        </div>
                    </div>
                    <div class="text-xs text-gray-600 space-y-1">
                        <p>• Condition: status != 'test'</p>
                        <p>• Est. output: ~44,500 rows</p>
                    </div>
                </div>

                <div class="pipeline-node node-destination" style="left: 600px; top: 150px;" id="node3">
                    <div class="node-port node-port-input"></div>
                    <div class="flex items-center space-x-3 mb-3">
                        <div class="w-10 h-10 bg-teal-100 rounded-lg flex items-center justify-center">
                            <i data-feather="hard-drive" class="w-5 h-5 text-teal-600"></i>
                        </div>
                        <div>
                            <h4 class="font-semibold text-gray-800">BigQuery</h4>
                            <p class="text-xs text-gray-500">analytics.orders_clean</p>
                        </div>
                    </div>
                    <div class="text-xs text-gray-600 space-y-1">
                        <p>• Dataset: analytics</p>
                        <p>• Table: orders_clean</p>
                        <p>• Mode: Append</p>
                    </div>
                </div>
            </div>

            <!-- Properties Panel -->
            <div class="properties-panel glassmorphism rounded-xl shadow-xl p-6" id="propertiesPanel">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-lg font-semibold text-gray-800">Node Properties</h3>
                    <button onclick="togglePropertiesPanel()" class="text-gray-400 hover:text-gray-600">
                        <i data-feather="x" class="w-5 h-5"></i>
                    </button>
                </div>

                <!-- Tabs -->
                <div class="flex space-x-1 mb-6 border-b border-gray-200">
                    <button class="tab-button active">Configuration</button>
                    <button class="tab-button">Schema</button>
                    <button class="tab-button">Preview</button>
                </div>

                <!-- Configuration Tab -->
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Node Name</label>
                        <input type="text" value="Filter Test Orders" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-teal-500">
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Filter Condition</label>
                        <div class="code-editor">
                            <pre><code>WHERE status != 'test'
  AND created_at >= '2025-01-01'
  AND total_amount > 0</code></pre>
                        </div>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Error Handling</label>
                        <select class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-teal-500">
                            <option>Skip and log error</option>
                            <option>Fail pipeline</option>
                            <option>Retry 3 times</option>
                        </select>
                    </div>

                    <div>
                        <label class="flex items-center space-x-3">
                            <div class="toggle-switch active" onclick="toggleSwitch(this)"></div>
                            <span class="text-sm text-gray-700">Enable data validation</span>
                        </label>
                    </div>

                    <div class="pt-4 border-t border-gray-200">
                        <button class="w-full px-4 py-2 bg-teal-50 text-teal-700 rounded-lg font-medium hover:bg-teal-100 transition-colors">
                            Apply Changes
                        </button>
                    </div>
                </div>
            </div>

            <!-- Bottom Status Bar -->
            <div class="absolute bottom-0 left-0 right-0 glassmorphism border-t border-gray-200">
                <div class="flex items-center justify-between px-6 py-3">
                    <div class="flex items-center space-x-6 text-sm">
                        <div class="flex items-center space-x-2">
                            <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                            <span class="text-gray-600">3 nodes</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
                            <span class="text-gray-600">2 connections</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <i data-feather="clock" class="w-4 h-4 text-gray-400"></i>
                            <span class="text-gray-600">Est. duration: 2-3 mins</span>
                        </div>
                    </div>
                    <div class="flex items-center space-x-3">
                        <button class="text-sm text-gray-600 hover:text-gray-800">
                            <i data-feather="maximize-2" class="w-4 h-4 inline mr-1"></i>
                            Fullscreen
                        </button>
                        <button class="text-sm text-gray-600 hover:text-gray-800">
                            <i data-feather="download" class="w-4 h-4 inline mr-1"></i>
                            Export
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Test Run Modal -->
    <div id="testRunModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-center justify-center p-6">
        <div class="glassmorphism rounded-xl max-w-2xl w-full animate-slide-in">
            <div class="p-6 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h3 class="text-xl font-bold text-gray-800">Test Pipeline Run</h3>
                    <button onclick="closeTestRunModal()" class="text-gray-400 hover:text-gray-600">
                        <i data-feather="x" class="w-5 h-5"></i>
                    </button>
                </div>
            </div>
            
            <div class="p-6">
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Sample Size</label>
                        <div class="flex items-center space-x-3">
                            <input type="range" min="10" max="1000" value="100" class="flex-1" id="sampleSize">
                            <span class="text-sm font-medium text-gray-800 w-16" id="sampleSizeValue">100</span>
                            <span class="text-sm text-gray-600">rows</span>
                        </div>
                    </div>

                    <div>
                        <label class="flex items-center space-x-3">
                            <input type="checkbox" checked class="rounded text-teal-600 focus:ring-teal-500">
                            <span class="text-sm text-gray-700">Enable debug logging</span>
                        </label>
                    </div>

                    <div>
                        <label class="flex items-center space-x-3">
                            <input type="checkbox" checked class="rounded text-teal-600 focus:ring-teal-500">
                            <span class="text-sm text-gray-700">Preview output data</span>
                        </label>
                    </div>

                    <div class="pt-4 border-t border-gray-200">
                        <div class="flex items-center justify-between">
                            <p class="text-sm text-gray-600">Estimated time: ~30 seconds</p>
                            <div class="flex items-center space-x-3">
                                <button onclick="closeTestRunModal()" class="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg font-medium hover:bg-gray-200">
                                    Cancel
                                </button>
                                <button class="px-4 py-2 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 flex items-center space-x-2">
                                    <i data-feather="play" class="w-4 h-4"></i>
                                    <span>Start Test Run</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Initialize Feather Icons
        feather.replace();

        // Initialize drag and drop
        interact('.pipeline-node')
            .draggable({
                inertia: true,
                modifiers: [
                    interact.modifiers.restrictRect({
                        restriction: 'parent',
                        endOnly: true
                    })
                ],
                autoScroll: true,
                listeners: {
                    move: dragMoveListener,
                    start: function(event) {
                        event.target.classList.add('dragging');
                    },
                    end: function(event) {
                        event.target.classList.remove('dragging');
                    }
                }
            });

        function dragMoveListener(event) {
            var target = event.target;
            var x = (parseFloat(target.getAttribute('data-x')) || 0) + event.dx;
            var y = (parseFloat(target.getAttribute('data-y')) || 0) + event.dy;

            target.style.transform = 'translate(' + x + 'px, ' + y + 'px)';
            target.setAttribute('data-x', x);
            target.setAttribute('data-y', y);
        }

        // Node selection
        document.querySelectorAll('.pipeline-node').forEach(node => {
            node.addEventListener('click', function() {
                document.querySelectorAll('.pipeline-node').forEach(n => n.classList.remove('selected'));
                this.classList.add('selected');
                showPropertiesPanel();
            });
        });

        // Properties panel
        function togglePropertiesPanel() {
            document.getElementById('propertiesPanel').classList.toggle('hidden');
        }

        function showPropertiesPanel() {
            document.getElementById('propertiesPanel').classList.remove('hidden');
        }

        // Toggle switch
        function toggleSwitch(element) {
            element.classList.toggle('active');
        }

        // Tab switching
        document.querySelectorAll('.tab-button').forEach(button => {
            button.addEventListener('click', function() {
                document.querySelectorAll('.tab-button').forEach(b => b.classList.remove('active'));
                this.classList.add('active');
            });
        });

        // Sample size slider
        const sampleSizeSlider = document.getElementById('sampleSize');
        const sampleSizeValue = document.getElementById('sampleSizeValue');
        
        if (sampleSizeSlider) {
            sampleSizeSlider.addEventListener('input', function() {
                sampleSizeValue.textContent = this.value;
            });
        }

        // Modal functions
        function openTestRunModal() {
            document.getElementById('testRunModal').classList.remove('hidden');
        }

        function closeTestRunModal() {
            document.getElementById('testRunModal').classList.add('hidden');
        }

        // Drag and drop from sidebar
        document.querySelectorAll('.component-item').forEach(item => {
            item.addEventListener('dragstart', function(e) {
                e.dataTransfer.setData('componentType', this.dataset.componentType);
                e.dataTransfer.setData('specificType', this.dataset[this.dataset.componentType + 'Type']);
            });
        });

        // Canvas drop handling
        const canvas = document.getElementById('pipelineCanvas');
        canvas.addEventListener('dragover', function(e) {
            e.preventDefault();
        });

        canvas.addEventListener('drop', function(e) {
            e.preventDefault();
            const componentType = e.dataTransfer.getData('componentType');
            const specificType = e.dataTransfer.getData('specificType');
            
            // Create new node at drop position
            console.log('Dropped:', componentType, specificType, 'at', e.clientX, e.clientY);
            // Implementation for creating new node would go here
        });
    </script>
</body>
</html>