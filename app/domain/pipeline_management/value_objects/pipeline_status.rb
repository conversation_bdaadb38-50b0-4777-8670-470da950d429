# frozen_string_literal: true

module Domain
  module PipelineManagement
    module ValueObjects
      # Represents the status of a pipeline with rich behavior
      class PipelineStatus
        include ActiveModel::Model

        attr_reader :value, :changed_at, :changed_by, :reason

        STATUSES = {
          draft: 'draft',
          active: 'active',
          paused: 'paused',
          archived: 'archived'
        }.freeze

        VALID_TRANSITIONS = {
          'draft' => %w[active archived],
          'active' => %w[paused archived],
          'paused' => %w[active archived],
          'archived' => []
        }.freeze

        def initialize(value:, changed_at: Time.current, changed_by: nil, reason: nil)
          raise ArgumentError, "Invalid status: #{value}" unless STATUSES.values.include?(value)
          
          @value = value
          @changed_at = changed_at
          @changed_by = changed_by
          @reason = reason
        end

        def draft?
          value == STATUSES[:draft]
        end

        def active?
          value == STATUSES[:active]
        end

        def paused?
          value == STATUSES[:paused]
        end

        def archived?
          value == STATUSES[:archived]
        end

        def can_transition_to?(new_status)
          return false if new_status == value
          return false unless STATUSES.values.include?(new_status)
          
          VALID_TRANSITIONS[value].include?(new_status)
        end

        def transition_to(new_status, changed_by: nil, reason: nil)
          unless can_transition_to?(new_status)
            raise ArgumentError, "Cannot transition from #{value} to #{new_status}"
          end

          self.class.new(
            value: new_status,
            changed_at: Time.current,
            changed_by: changed_by,
            reason: reason
          )
        end

        def available_transitions
          VALID_TRANSITIONS[value].dup
        end

        def executable?
          active?
        end

        def editable?
          !archived?
        end

        def deletable?
          draft? || archived?
        end

        def requires_reason_for_transition?
          %w[paused archived].include?(value)
        end

        def duration_in_status
          Time.current - changed_at
        end

        def to_s
          value
        end

        def to_h
          {
            value: value,
            changed_at: changed_at,
            changed_by: changed_by,
            reason: reason
          }.compact
        end

        def ==(other)
          return false unless other.is_a?(self.class)
          value == other.value
        end

        class << self
          def draft(changed_by: nil)
            new(value: STATUSES[:draft], changed_by: changed_by)
          end

          def active(changed_by: nil)
            new(value: STATUSES[:active], changed_by: changed_by)
          end

          def paused(changed_by: nil, reason: nil)
            new(value: STATUSES[:paused], changed_by: changed_by, reason: reason)
          end

          def archived(changed_by: nil, reason: nil)
            new(value: STATUSES[:archived], changed_by: changed_by, reason: reason)
          end

          def from_string(status_string, **options)
            new(value: status_string, **options)
          end
        end
      end
    end
  end
end