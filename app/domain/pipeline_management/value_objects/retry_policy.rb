# frozen_string_literal: true

module Domain
  module PipelineManagement
    module ValueObjects
      # Represents retry behavior for pipeline executions
      class RetryPolicy
        include ActiveModel::Model
        include ActiveModel::Validations

        attr_reader :max_attempts, :backoff_strategy, :initial_delay, :max_delay, :multiplier

        BACKOFF_STRATEGIES = %w[constant linear exponential fibonacci].freeze
        DEFAULT_INITIAL_DELAY = 60 # seconds
        DEFAULT_MAX_DELAY = 3600 # 1 hour
        DEFAULT_MULTIPLIER = 2

        validates :max_attempts, presence: true, numericality: { 
          greater_than: 0, 
          less_than_or_equal_to: 10,
          only_integer: true 
        }
        validates :backoff_strategy, inclusion: { in: BACKOFF_STRATEGIES }
        validates :initial_delay, numericality: { greater_than: 0 }
        validates :max_delay, numericality: { greater_than: 0 }
        validates :multiplier, numericality: { greater_than: 1 }, if: :exponential_backoff?
        validate :validate_delay_consistency

        def initialize(max_attempts:, backoff_strategy: 'exponential', 
                       initial_delay: DEFAULT_INITIAL_DELAY, 
                       max_delay: DEFAULT_MAX_DELAY,
                       multiplier: DEFAULT_MULTIPLIER)
          @max_attempts = max_attempts.to_i
          @backoff_strategy = backoff_strategy
          @initial_delay = initial_delay.to_i
          @max_delay = max_delay.to_i
          @multiplier = multiplier.to_f
          validate!
        end

        def delay_for_attempt(attempt_number)
          return nil if attempt_number > max_attempts
          return 0 if attempt_number <= 0

          delay = case backoff_strategy
                  when 'constant'
                    initial_delay
                  when 'linear'
                    initial_delay * attempt_number
                  when 'exponential'
                    initial_delay * (multiplier ** (attempt_number - 1))
                  when 'fibonacci'
                    calculate_fibonacci_delay(attempt_number)
                  end

          [delay, max_delay].min
        end

        def should_retry?(attempt_number, error = nil)
          return false if attempt_number >= max_attempts
          return false if error && non_retryable_error?(error)
          true
        end

        def exhausted?(attempt_number)
          attempt_number >= max_attempts
        end

        def to_h
          {
            max_attempts: max_attempts,
            backoff_strategy: backoff_strategy,
            initial_delay: initial_delay,
            max_delay: max_delay,
            multiplier: multiplier
          }
        end

        def ==(other)
          return false unless other.is_a?(self.class)

          max_attempts == other.max_attempts &&
            backoff_strategy == other.backoff_strategy &&
            initial_delay == other.initial_delay &&
            max_delay == other.max_delay &&
            multiplier == other.multiplier
        end

        private

        def exponential_backoff?
          backoff_strategy == 'exponential'
        end

        def validate_delay_consistency
          if initial_delay > max_delay
            errors.add(:initial_delay, 'cannot be greater than max_delay')
          end
        end

        def calculate_fibonacci_delay(attempt_number)
          return initial_delay if attempt_number == 1
          return initial_delay * 2 if attempt_number == 2

          fib_sequence = [1, 2]
          (3..attempt_number).each do
            fib_sequence << fib_sequence[-1] + fib_sequence[-2]
          end

          initial_delay * fib_sequence[attempt_number - 1]
        end

        def non_retryable_error?(error)
          # Define errors that should not trigger retries
          case error
          when String
            error.match?(/authentication|authorization|invalid credentials|forbidden/i)
          when StandardError
            error.is_a?(ArgumentError) || 
            error.is_a?(NoMethodError) ||
            error.message.match?(/authentication|authorization|invalid credentials|forbidden/i)
          else
            false
          end
        end
      end
    end
  end
end