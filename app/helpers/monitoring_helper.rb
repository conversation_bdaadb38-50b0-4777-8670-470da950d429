module MonitoringHelper
  def event_icon(event_type)
    case event_type
    when 'pipeline_started', 'pipeline_completed'
      'activity'
    when 'error', 'failure'
      'alert-circle'
    when 'warning'
      'alert-triangle'
    when 'success'
      'check-circle'
    when 'user_action'
      'user'
    when 'system'
      'server'
    when 'data_sync'
      'refresh-cw'
    when 'configuration'
      'settings'
    else
      'info'
    end
  end
  
  def health_percentage(health_status)
    return 0 unless health_status.is_a?(Hash) && health_status[:checks].is_a?(Hash)
    
    total_checks = health_status[:checks].size
    healthy_checks = health_status[:checks].values.count { |check| check[:status] == 'healthy' }
    
    return 100 if total_checks == 0
    ((healthy_checks.to_f / total_checks) * 100).round
  end
  
  def format_resource_usage(value)
    return '0%' if value.nil?
    "#{value.round}%"
  end
  
  def pipeline_status_color(status)
    case status
    when 'running'
      'teal'
    when 'completed'
      'green'
    when 'failed'
      'red'
    when 'pending', 'queued'
      'yellow'
    else
      'gray'
    end
  end
  
  def severity_icon_color(severity)
    case severity
    when 'critical'
      'red'
    when 'warning'
      'yellow'
    when 'info'
      'blue'
    else
      'gray'
    end
  end
end