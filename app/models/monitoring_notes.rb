# frozen_string_literal: true

# Add these methods to your existing models if they don't exist

# In app/models/pipeline_execution.rb
# Add these methods if not present:
#
# def progress_percentage
#   return 0 if total_records.nil? || total_records.zero?
#   return 100 if status == 'completed'
#   return 0 if status == 'pending' || status == 'initializing'
#   
#   ((records_processed.to_f / total_records) * 100).round(1)
# end
#
# def destination_type
#   destination_config['type'] || 'data_warehouse'
# end

# In app/models/system_metric.rb
# Add this method if not present:
#
# def to_percentage_hash
#   {
#     cpu_usage: cpu_usage || 0,
#     memory_usage: memory_usage || 0,
#     storage_usage: storage_usage || 0
#   }
# end
