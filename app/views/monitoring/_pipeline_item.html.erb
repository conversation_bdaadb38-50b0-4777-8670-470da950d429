<div class="pipeline-item">
  <div class="pipeline-header">
    <div class="pipeline-info">
      <h4 class="pipeline-name"><%= pipeline.pipeline_name %></h4>
      <p class="pipeline-source"><%= pipeline.data_source&.source_type&.humanize || "Manual" %> → BigQuery</p>
    </div>
    <div class="pipeline-status <%= pipeline.status %>">
      <%= pipeline.status.capitalize %>
    </div>
    <div class="pipeline-actions">
      <button class="icon-btn" title="View Details">
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
        </svg>
      </button>
      <button class="icon-btn" title="Cancel">
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
        </svg>
      </button>
    </div>
  </div>
  
  <div class="pipeline-progress">
    <div class="progress-bar">
      <div class="progress-bar-fill" style="width: <%= pipeline.progress || 0 %>%"></div>
    </div>
    <div class="progress-details">
      <span><%= pipeline.progress || 0 %>% • <%= number_with_delimiter(pipeline.records_processed || 0) %> / <%= number_with_delimiter(pipeline.total_records || 45000) %> records</span>
    </div>
  </div>
  
  <div class="pipeline-stats">
    <div class="stat-item">
      <span class="stat-label">Duration</span>
      <span class="stat-value"><%= pipeline.started_at ? "#{((Time.current - pipeline.started_at) / 60).round}m #{((Time.current - pipeline.started_at) % 60).round}s" : "0m" %></span>
    </div>
    <div class="stat-item">
      <span class="stat-label">Speed</span>
      <span class="stat-value"><%= number_with_delimiter(pipeline.records_per_second || 1250) %>/s</span>
    </div>
    <div class="stat-item">
      <span class="stat-label">ETA</span>
      <span class="stat-value"><%= pipeline.estimated_completion ? "#{((pipeline.estimated_completion - Time.current) / 60).round}m" : "2m 10s" %></span>
    </div>
    <div class="stat-item">
      <span class="stat-label">CPU</span>
      <span class="stat-value"><%= pipeline.cpu_usage || 35 %>%</span>
    </div>
    <div class="stat-item">
      <span class="stat-label">Memory</span>
      <span class="stat-value"><%= number_to_human_size((pipeline.memory_usage_gb || 2.1) * 1024 * 1024 * 1024) %></span>
    </div>
  </div>
  
  <% if pipeline.status == 'initializing' %>
    <div class="pipeline-message">
      <svg class="w-4 h-4 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
      </svg>
      <span>Connecting to source database...</span>
    </div>
  <% end %>
</div>