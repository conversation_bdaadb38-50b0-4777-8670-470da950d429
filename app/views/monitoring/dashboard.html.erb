<% content_for :page_title, "System Monitoring" %>
<% content_for :page_subtitle, "Real-time monitoring of pipelines, system health, and performance metrics" %>

<div class="dashboard-content">
  <!-- Dashboard Header -->
  <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-8">
    <div class="mb-4 lg:mb-0">
      <h1 class="text-2xl font-bold mb-2" style="color: var(--color-text);">System Monitoring</h1>
      <p style="color: var(--color-text-secondary);">Real-time monitoring of pipelines, system health, and performance metrics</p>
    </div>
    <div class="flex flex-wrap gap-3 items-center">
      <!-- Time Range Selector -->
      <div data-controller="dropdown" class="relative">
        <button data-action="click->dropdown#toggle" class="btn btn--outline btn--sm inline-flex items-center gap-2">
          Last 24 Hours
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
          </svg>
        </button>
        <div data-dropdown-target="menu" class="hidden absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg z-50" style="background-color: var(--color-surface); border: 1px solid var(--color-card-border);">
          <a href="#" class="block px-4 py-2 text-sm hover:bg-gray-50" style="color: var(--color-text);">Last Hour</a>
          <a href="#" class="block px-4 py-2 text-sm hover:bg-gray-50" style="color: var(--color-text);">Last 24 Hours</a>
          <a href="#" class="block px-4 py-2 text-sm hover:bg-gray-50" style="color: var(--color-text);">Last 7 Days</a>
          <a href="#" class="block px-4 py-2 text-sm hover:bg-gray-50" style="color: var(--color-text);">Last 30 Days</a>
        </div>
      </div>

      <!-- Refresh Button -->
      <%= link_to monitoring_dashboard_path, class: "btn btn--outline btn--sm inline-flex items-center gap-2",
          data: { turbo_method: :get } do %>
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
        </svg>
        Refresh
      <% end %>

      <!-- Export Report -->
      <button class="btn btn--primary btn--sm inline-flex items-center gap-2" 
              data-controller="export-handler" data-action="click->export-handler#exportReport">
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
        </svg>
        Export Report
      </button>
    </div>
  </div>

  <!-- Health Overview Metrics -->
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
    <!-- System Health -->
    <div class="metric-card">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-xs font-semibold uppercase tracking-wide" style="color: var(--color-text-secondary);">System Health</h3>
        <% health_score = calculate_health_score(@system_health) %>
        <div class="flex items-center gap-1">
          <div class="w-2 h-2 rounded-full <%= health_score >= 80 ? 'bg-green-500' : 'bg-yellow-500' %>"></div>
          <span class="text-sm <%= health_score >= 80 ? 'text-green-600' : 'text-yellow-600' %>">
            <%= health_status_label(health_score) %>
          </span>
        </div>
      </div>
      <div class="flex items-center gap-6">
        <div class="relative">
          <svg class="w-20 h-20 transform -rotate-90">
            <circle cx="40" cy="40" r="32" stroke="#e5e7eb" stroke-width="6" fill="none"></circle>
            <circle cx="40" cy="40" r="32" 
                    stroke="<%= health_score >= 80 ? '#10b981' : '#f59e0b' %>" 
                    stroke-width="6" 
                    fill="none"
                    stroke-dasharray="201" 
                    stroke-dashoffset="<%= 201 - (201 * health_score / 100) %>"
                    stroke-linecap="round"></circle>
          </svg>
          <div class="absolute inset-0 flex items-center justify-center">
            <span class="text-2xl font-bold" style="color: var(--color-text);"><%= health_score %>%</span>
          </div>
        </div>
        <div>
          <p class="text-sm" style="color: var(--color-text-secondary);">Uptime</p>
          <p class="text-xl font-bold" style="color: var(--color-text);">
            <%= calculate_uptime_percentage(@system_health) %>%
          </p>
          <p class="text-xs mt-1" style="color: var(--color-text-secondary);">30 days</p>
        </div>
      </div>
    </div>

    <!-- Active Pipelines -->
    <div class="metric-card">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-xs font-semibold uppercase tracking-wide" style="color: var(--color-text-secondary);">Active Pipelines</h3>
        <div class="w-8 h-8 rounded-lg flex items-center justify-center" style="background-color: var(--color-primary-light);">
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" style="color: var(--color-primary);">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
          </svg>
        </div>
      </div>
      <div class="mb-4">
        <p class="text-sm mb-1" style="color: var(--color-text-secondary);">Running</p>
        <p class="text-2xl font-bold" style="color: var(--color-text);"><%= @active_pipelines.where(status: 'running').count %></p>
      </div>
      <div class="space-y-2">
        <% 
          running_count = @active_pipelines.where(status: 'running').count
          pending_count = @active_pipelines.where(status: 'pending').count
          failed_count = current_organization.pipeline_executions.where(status: 'failed', created_at: 24.hours.ago..Time.current).count
          total_active = [running_count + pending_count + failed_count, 1].max
        %>
        <div class="w-full h-2 bg-gray-200 rounded-full overflow-hidden">
          <div class="h-full bg-green-500 rounded-full" style="width: <%= (running_count.to_f / total_active * 100).round %>%"></div>
        </div>
        <div class="flex items-center justify-between text-sm">
          <span class="flex items-center gap-2">
            <span class="w-2 h-2 bg-yellow-500 rounded-full"></span>
            <span style="color: var(--color-text-secondary);">Queued: <%= pending_count %></span>
          </span>
          <span class="flex items-center gap-2">
            <span class="w-2 h-2 bg-red-500 rounded-full"></span>
            <span style="color: var(--color-text-secondary);">Failed: <%= failed_count %></span>
          </span>
        </div>
      </div>
    </div>

    <!-- Resource Usage -->
    <div class="metric-card">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-xs font-semibold uppercase tracking-wide" style="color: var(--color-text-secondary);">Resource Usage</h3>
        <div class="w-8 h-8 rounded-lg flex items-center justify-center" style="background-color: #f3e8ff;">
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" style="color: #9333ea;">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z"/>
          </svg>
        </div>
      </div>
      <div class="space-y-3">
        <div>
          <div class="flex items-center justify-between text-sm mb-1">
            <span style="color: var(--color-text-secondary);">CPU</span>
            <span class="font-semibold" style="color: var(--color-text);"><%= @current_metrics[:cpu_usage].round(1) %>%</span>
          </div>
          <div class="w-full h-2 bg-gray-200 rounded-full overflow-hidden">
            <div class="h-full bg-purple-500 rounded-full" style="width: <%= @current_metrics[:cpu_usage] %>%"></div>
          </div>
        </div>
        <div>
          <div class="flex items-center justify-between text-sm mb-1">
            <span style="color: var(--color-text-secondary);">Memory</span>
            <span class="font-semibold" style="color: var(--color-text);"><%= @current_metrics[:memory_usage].round(1) %>%</span>
          </div>
          <div class="w-full h-2 bg-gray-200 rounded-full overflow-hidden">
            <div class="h-full bg-orange-500 rounded-full" style="width: <%= @current_metrics[:memory_usage] %>%"></div>
          </div>
        </div>
        <div>
          <div class="flex items-center justify-between text-sm mb-1">
            <span style="color: var(--color-text-secondary);">Storage</span>
            <span class="font-semibold" style="color: var(--color-text);"><%= @current_metrics[:storage_usage].round(1) %>%</span>
          </div>
          <div class="w-full h-2 bg-gray-200 rounded-full overflow-hidden">
            <div class="h-full bg-red-500 rounded-full" style="width: <%= @current_metrics[:storage_usage] %>%"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- Active Alerts -->
    <div class="metric-card">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-xs font-semibold uppercase tracking-wide" style="color: var(--color-text-secondary);">Active Alerts</h3>
        <div class="w-8 h-8 rounded-lg flex items-center justify-center" style="background-color: #fee2e2;">
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" style="color: #ef4444;">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
          </svg>
        </div>
      </div>
      <div class="space-y-3">
        <% alert_counts = count_alerts_by_severity(@recent_alerts) %>
        <% if alert_counts.values.sum > 0 %>
          <% alert_counts.each do |severity, count| %>
            <% next if count == 0 %>
            <div class="flex items-center justify-between">
              <div class="flex items-center gap-2">
                <span class="w-2 h-2 rounded-full <%= alert_severity_color(severity) %>"></span>
                <span class="text-sm" style="color: var(--color-text);"><%= severity.humanize %></span>
              </div>
              <span class="text-xl font-bold <%= alert_severity_text_color(severity) %>">
                <%= count %>
              </span>
            </div>
          <% end %>
        <% else %>
          <div class="text-center py-2">
            <svg class="w-6 h-6 mx-auto mb-2 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
            </svg>
            <p class="text-sm font-medium" style="color: var(--color-text);">No Active Alerts</p>
          </div>
        <% end %>
      </div>
      <%= link_to "#", class: "text-sm font-medium mt-4 inline-block", style: "color: var(--color-primary);" do %>
        View All →
      <% end %>
    </div>
  </div>

  <!-- Main Monitoring Sections -->
  <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
    <!-- Live Pipeline Activity -->
    <div class="lg:col-span-2">
      <div class="metric-card">
        <div class="flex items-center justify-between mb-6">
          <div class="flex items-center gap-3">
            <h3 class="text-lg font-semibold" style="color: var(--color-text);">Live Pipeline Activity</h3>
            <div class="flex items-center gap-2">
              <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
              <span class="text-sm text-green-600">Live</span>
            </div>
          </div>
          <div class="flex items-center gap-2">
            <span class="text-sm" style="color: var(--color-text-secondary);">Auto-refresh</span>
            <div data-controller="toggle-switch" data-toggle-switch-active-value="true">
              <button data-action="click->toggle-switch#toggle" 
                      class="relative w-12 h-6 bg-green-500 rounded-full cursor-pointer transition-colors">
                <span class="absolute top-0.5 left-0.5 w-5 h-5 bg-white rounded-full transform translate-x-6 transition-transform"></span>
              </button>
            </div>
          </div>
        </div>
        
        <div class="space-y-4" data-controller="pipeline-monitor" data-pipeline-monitor-refresh-url-value="<%= monitoring_dashboard_path %>">
          <% if @active_pipelines.any? %>
            <% @active_pipelines.limit(3).each do |pipeline| %>
              <%= render 'monitoring/pipeline_activity_item', pipeline: pipeline %>
            <% end %>
          <% else %>
            <div class="text-center py-12">
              <svg class="w-12 h-12 mx-auto mb-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
              </svg>
              <p class="text-lg font-medium" style="color: var(--color-text);">No Active Pipelines</p>
              <p class="text-sm" style="color: var(--color-text-secondary);">All pipelines are currently idle</p>
            </div>
          <% end %>
        </div>
      </div>
    </div>

    <!-- Right Column -->
    <div class="space-y-6">
      <!-- Real-time Metrics Chart -->
      <div class="metric-card">
        <h3 class="text-lg font-semibold mb-4" style="color: var(--color-text);">Real-time Metrics</h3>
        <div class="relative h-48">
          <canvas id="realtimeChart" 
                  data-controller="realtime-chart" 
                  data-realtime-chart-metrics-value="<%= resource_metrics_for_chart(@resource_metrics).to_json %>"></canvas>
        </div>
      </div>
      
      <!-- Recent Alerts -->
      <div class="metric-card">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-semibold" style="color: var(--color-text);">Recent Alerts</h3>
          <%= link_to "#", class: "text-sm font-medium", style: "color: var(--color-primary);" do %>
            View All
          <% end %>
        </div>
        
        <div class="space-y-3">
          <% if @recent_alerts.any? %>
            <% @recent_alerts.limit(3).each do |alert| %>
              <%= render 'monitoring/alert_compact_item', alert: alert %>
            <% end %>
          <% else %>
            <div class="text-center py-6">
              <svg class="w-8 h-8 mx-auto mb-2 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
              </svg>
              <p class="text-sm font-medium" style="color: var(--color-text);">No Active Alerts</p>
              <p class="text-xs" style="color: var(--color-text-secondary);">All systems are running normally</p>
            </div>
          <% end %>
        </div>
      </div>
    </div>
  </div>

  <!-- Performance Charts -->
  <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
    <div class="metric-card">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-semibold" style="color: var(--color-text);">Pipeline Performance</h3>
        <div data-controller="dropdown" class="relative">
          <button data-action="click->dropdown#toggle" class="btn btn--outline btn--sm inline-flex items-center gap-2">
            Today
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
            </svg>
          </button>
        </div>
      </div>
      <div class="relative h-72">
        <canvas id="performanceChart" 
                data-controller="performance-chart"
                data-performance-chart-data-value="<%= @pipeline_performance.to_json %>"></canvas>
      </div>
    </div>
    
    <div class="metric-card">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-semibold" style="color: var(--color-text);">Resource Utilization</h3>
        <div data-controller="dropdown" class="relative">
          <button data-action="click->dropdown#toggle" class="btn btn--outline btn--sm inline-flex items-center gap-2">
            Last Hour
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
            </svg>
          </button>
        </div>
      </div>
      <div class="relative h-72">
        <canvas id="resourceChart" 
                data-controller="resource-chart"
                data-resource-chart-metrics-value="<%= resource_metrics_for_chart(@resource_metrics).to_json %>"></canvas>
      </div>
    </div>
  </div>

  <!-- Event Timeline & Logs -->
  <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
    <!-- Event Timeline -->
    <div class="metric-card">
      <h3 class="text-lg font-semibold mb-6" style="color: var(--color-text);">Event Timeline</h3>
      
      <div class="relative">
        <% if @timeline_events.any? %>
          <% @timeline_events.limit(4).each do |event| %>
            <%= render 'monitoring/timeline_event_item', event: event %>
          <% end %>
        <% else %>
          <div class="text-center py-8">
            <svg class="w-12 h-12 mx-auto mb-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
            </svg>
            <p class="text-lg font-medium" style="color: var(--color-text);">No Recent Events</p>
            <p class="text-sm" style="color: var(--color-text-secondary);">System events will appear here</p>
          </div>
        <% end %>
      </div>
    </div>

    <!-- System Logs -->
    <div class="metric-card">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-semibold" style="color: var(--color-text);">System Logs</h3>
        <div class="flex items-center gap-2">
          <div data-controller="dropdown" class="relative">
            <button data-action="click->dropdown#toggle" class="btn btn--outline btn--sm inline-flex items-center gap-2">
              All Levels
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
              </svg>
            </button>
          </div>
          <button class="btn btn--outline btn--sm" title="Download Logs">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
            </svg>
          </button>
        </div>
      </div>
      
      <div class="bg-gray-900 text-gray-100 font-mono text-sm rounded-lg p-4 overflow-auto max-h-72" data-controller="log-viewer">
        <% log_entries = generate_log_entries(@timeline_events, @recent_alerts) %>
        <% if log_entries.any? %>
          <% log_entries.first(8).each do |log| %>
            <div class="flex items-start gap-3 mb-1">
              <span class="text-gray-500 text-xs flex-shrink-0 w-16"><%= log[:timestamp].strftime('%H:%M:%S') %></span>
              <span class="flex-shrink-0 w-12 font-semibold <%= case log[:level]
                when 'INFO' then 'text-blue-400'
                when 'WARN' then 'text-yellow-400'
                when 'ERROR' then 'text-red-400'
                else 'text-gray-400'
                end %>">
                <%= log[:level] %>
              </span>
              <span class="flex-1 text-gray-100"><%= truncate(log[:message], length: 70) %></span>
            </div>
          <% end %>
        <% else %>
          <div class="text-center py-4 text-gray-500">
            <p>No recent log entries</p>
          </div>
        <% end %>
      </div>
    </div>
  </div>
</div>

<!-- Chart.js Integration -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
  // Chart.js defaults for consistent styling
  Chart.defaults.font.family = 'Inter, -apple-system, BlinkMacSystemFont, sans-serif';
  Chart.defaults.color = getComputedStyle(document.documentElement).getPropertyValue('--color-text-secondary') || '#6b7280';
  
  // Real-time metrics chart
  const realtimeCtx = document.getElementById('realtimeChart');
  if (realtimeCtx) {
    const metricsData = JSON.parse(realtimeCtx.dataset.realtimeChartMetricsValue || '[]');
    
    new Chart(realtimeCtx, {
      type: 'line',
      data: {
        labels: metricsData.map(m => m.timestamp),
        datasets: [
          {
            label: 'CPU',
            data: metricsData.map(m => m.cpu),
            borderColor: '#8b5cf6',
            backgroundColor: '#8b5cf6',
            tension: 0.4,
            pointRadius: 0,
            pointHoverRadius: 4
          },
          {
            label: 'Memory',
            data: metricsData.map(m => m.memory),
            borderColor: '#f97316',
            backgroundColor: '#f97316',
            tension: 0.4,
            pointRadius: 0,
            pointHoverRadius: 4
          }
        ]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: { legend: { position: 'bottom' } },
        scales: {
          y: { beginAtZero: true, max: 100 },
          x: { display: false }
        }
      }
    });
  }

  // Performance chart
  const performanceCtx = document.getElementById('performanceChart');
  if (performanceCtx) {
    const performanceData = JSON.parse(performanceCtx.dataset.performanceChartDataValue || '{}');
    
    new Chart(performanceCtx, {
      type: 'bar',
      data: {
        labels: Object.keys(performanceData),
        datasets: [
          {
            label: 'Successful',
            data: Object.values(performanceData).map(d => d.completed || 0),
            backgroundColor: '#10b981'
          },
          {
            label: 'Failed',
            data: Object.values(performanceData).map(d => d.failed || 0),
            backgroundColor: '#ef4444'
          }
        ]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: { legend: { position: 'bottom' } },
        scales: { y: { beginAtZero: true } }
      }
    });
  }

  // Resource utilization chart
  const resourceCtx = document.getElementById('resourceChart');
  if (resourceCtx) {
    const resourceData = JSON.parse(resourceCtx.dataset.resourceChartMetricsValue || '[]');
    
    new Chart(resourceCtx, {
      type: 'line',
      data: {
        labels: resourceData.map(m => m.timestamp),
        datasets: [
          {
            label: 'CPU Usage',
            data: resourceData.map(m => m.cpu),
            borderColor: '#8b5cf6',
            fill: false,
            tension: 0.4
          },
          {
            label: 'Memory Usage',
            data: resourceData.map(m => m.memory),
            borderColor: '#f97316',
            fill: false,
            tension: 0.4
          },
          {
            label: 'Storage Usage',
            data: resourceData.map(m => m.storage),
            borderColor: '#ef4444',
            fill: false,
            tension: 0.4
          }
        ]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: { legend: { position: 'bottom' } },
        scales: {
          y: { beginAtZero: true, max: 100 },
          x: { display: true }
        }
      }
    });
  }
});
</script>
