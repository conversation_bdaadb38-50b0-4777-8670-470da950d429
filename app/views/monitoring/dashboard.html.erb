<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Data Refinery Platform - Monitoring</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/feather-icons/4.29.0/feather.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        :root {
            --bg-cream: #fdfdfc;
            --teal-primary: #14B8A6;
            --teal-dark: #0D9488;
            --teal-light: #5EEAD4;
            --teal-50: #F0FDFA;
            --gray-800: #1F2937;
            --gray-600: #4B5563;
            --gray-400: #9CA3AF;
            --success: #10B981;
            --warning: #F59E0B;
            --error: #EF4444;
            --info: #3B82F6;
        }
        
        body {
            background-color: var(--bg-cream);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        
        .glassmorphism {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.4);
            box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.08);
        }
        
        .glassmorphism-dark {
            background: rgba(31, 41, 55, 0.95);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            color: white;
        }
        
        .teal-gradient {
            background: linear-gradient(135deg, var(--teal-primary) 0%, var(--teal-dark) 100%);
        }
        
        /* Status Indicators */
        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            position: relative;
        }
        
        .status-dot::after {
            content: '';
            position: absolute;
            top: -4px;
            left: -4px;
            right: -4px;
            bottom: -4px;
            border-radius: 50%;
            background: inherit;
            opacity: 0.3;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0%, 100% { 
                opacity: 0.3;
                transform: scale(1);
            }
            50% { 
                opacity: 0.1;
                transform: scale(1.2);
            }
        }
        
        /* Health Score Ring */
        .health-ring {
            transform: rotate(-90deg);
        }
        
        .health-ring-circle {
            transition: stroke-dashoffset 0.5s ease;
        }
        
        /* Timeline */
        .timeline-item {
            position: relative;
            padding-left: 30px;
        }
        
        .timeline-item::before {
            content: '';
            position: absolute;
            left: 8px;
            top: 24px;
            bottom: -20px;
            width: 2px;
            background: #e5e7eb;
        }
        
        .timeline-item:last-child::before {
            display: none;
        }
        
        .timeline-dot {
            position: absolute;
            left: 0;
            top: 6px;
            width: 18px;
            height: 18px;
            border-radius: 50%;
            background: white;
            border: 2px solid #e5e7eb;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .timeline-dot.success {
            border-color: var(--success);
        }
        
        .timeline-dot.error {
            border-color: var(--error);
        }
        
        .timeline-dot.warning {
            border-color: var(--warning);
        }
        
        .timeline-dot.info {
            border-color: var(--info);
        }
        
        /* Log viewer */
        .log-viewer {
            font-family: 'Monaco', 'Consolas', 'Courier New', monospace;
            font-size: 12px;
            line-height: 1.6;
            background: #1e293b;
            color: #e2e8f0;
            padding: 16px;
            border-radius: 8px;
            overflow: auto;
            max-height: 400px;
        }
        
        .log-line {
            display: flex;
            align-items: flex-start;
            margin-bottom: 4px;
        }
        
        .log-timestamp {
            color: #64748b;
            margin-right: 12px;
            flex-shrink: 0;
        }
        
        .log-level {
            font-weight: bold;
            margin-right: 8px;
            flex-shrink: 0;
            width: 50px;
        }
        
        .log-level.info { color: #3b82f6; }
        .log-level.success { color: #10b981; }
        .log-level.warning { color: #f59e0b; }
        .log-level.error { color: #ef4444; }
        
        .log-message {
            flex: 1;
        }
        
        /* Metric Cards */
        .metric-card {
            position: relative;
            overflow: hidden;
        }
        
        .metric-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            transition: left 0.5s;
        }
        
        .metric-card:hover::before {
            left: 100%;
        }
        
        /* Animations */
        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        
        @keyframes rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
        
        .animate-slide-in {
            animation: slideIn 0.5s ease-out;
        }
        
        .animate-fade-in {
            animation: fadeIn 0.3s ease-out;
        }
        
        .rotate-animation {
            animation: rotate 2s linear infinite;
        }
        
        /* Progress bars */
        .progress-bar {
            position: relative;
            overflow: hidden;
        }
        
        .progress-bar::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            bottom: 0;
            width: 40%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
            animation: shimmer 1.5s infinite;
        }
        
        @keyframes shimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(250%); }
        }
        
        /* Alert styles */
        .alert-item {
            border-left: 4px solid;
            transition: all 0.3s ease;
        }
        
        .alert-item:hover {
            transform: translateX(4px);
        }
        
        .alert-item.critical {
            border-left-color: var(--error);
            background: rgba(239, 68, 68, 0.05);
        }
        
        .alert-item.warning {
            border-left-color: var(--warning);
            background: rgba(245, 158, 11, 0.05);
        }
        
        .alert-item.info {
            border-left-color: var(--info);
            background: rgba(59, 130, 246, 0.05);
        }
        
        /* Toggle Switch */
        .toggle-switch {
            position: relative;
            width: 44px;
            height: 24px;
            background: var(--gray-400);
            border-radius: 12px;
            cursor: pointer;
            transition: background 0.3s;
        }
        
        .toggle-switch.active {
            background: var(--teal-primary);
        }
        
        .toggle-switch::after {
            content: '';
            position: absolute;
            top: 2px;
            left: 2px;
            width: 20px;
            height: 20px;
            background: white;
            border-radius: 50%;
            transition: transform 0.3s;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }
        
        .toggle-switch.active::after {
            transform: translateX(20px);
        }
        
        /* Custom Scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }
        
        ::-webkit-scrollbar-track {
            background: rgba(0, 0, 0, 0.05);
            border-radius: 4px;
        }
        
        ::-webkit-scrollbar-thumb {
            background: var(--teal-primary);
            border-radius: 4px;
        }
        
        ::-webkit-scrollbar-thumb:hover {
            background: var(--teal-dark);
        }
    </style>
</head>
<body class="min-h-screen">
    <!-- Main Content -->
    <main class="pt-20 pb-8">
        <div class="container mx-auto px-6 py-8">
            <!-- Page Header -->
            <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-8">
                <div class="mb-4 lg:mb-0">
                    <h2 class="text-3xl font-bold text-gray-800 mb-2">System Monitoring</h2>
                    <p class="text-gray-600">Real-time monitoring of pipelines, system health, and performance metrics</p>
                </div>
                <div class="flex flex-wrap gap-3">
                    <select class="px-4 py-2 bg-white border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-teal-500 text-sm">
                        <option>Last 24 Hours</option>
                        <option>Last 7 Days</option>
                        <option>Last 30 Days</option>
                        <option>Custom Range</option>
                    </select>
                    <button class="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg font-medium hover:bg-gray-200 transition-colors flex items-center space-x-2">
                        <i data-feather="refresh-cw" class="w-4 h-4"></i>
                        <span>Refresh</span>
                    </button>
                    <button class="px-4 py-2 teal-gradient text-white rounded-lg font-medium hover:opacity-90 transition-opacity flex items-center space-x-2">
                        <i data-feather="download" class="w-4 h-4"></i>
                        <span>Export Report</span>
                    </button>
                </div>
            </div>

            <!-- Health Overview -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <!-- System Health -->
                <div class="glassmorphism rounded-xl p-6 metric-card animate-slide-in">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-sm font-semibold text-gray-600 uppercase tracking-wider">System Health</h3>
                        <div class="flex items-center space-x-1">
                            <% health_percentage = health_percentage(@system_health) %>
                            <% health_status = health_percentage >= 80 ? 'healthy' : (health_percentage >= 60 ? 'warning' : 'critical') %>
                            <div class="status-dot <%= health_status == 'healthy' ? 'bg-green-500' : (health_status == 'warning' ? 'bg-yellow-500' : 'bg-red-500') %>"></div>
                            <span class="text-xs <%= health_status == 'healthy' ? 'text-green-600' : (health_status == 'warning' ? 'text-yellow-600' : 'text-red-600') %> font-medium">
                                <%= health_status.humanize %>
                            </span>
                        </div>
                    </div>
                    <div class="flex items-center space-x-4">
                        <div class="relative w-20 h-20">
                            <svg class="health-ring w-20 h-20">
                                <circle cx="40" cy="40" r="36" stroke="#e5e7eb" stroke-width="8" fill="none"></circle>
                                <% stroke_color = health_status == 'healthy' ? '#10B981' : (health_status == 'warning' ? '#F59E0B' : '#EF4444') %>
                                <% stroke_offset = 226 - (226 * health_percentage / 100) %>
                                <circle class="health-ring-circle" cx="40" cy="40" r="36"
                                        stroke="<%= stroke_color %>" stroke-width="8" fill="none"
                                        stroke-dasharray="226" stroke-dashoffset="<%= stroke_offset %>" stroke-linecap="round"></circle>
                            </svg>
                            <div class="absolute inset-0 flex items-center justify-center">
                                <span class="text-2xl font-bold text-gray-800"><%= health_percentage %>%</span>
                            </div>
                        </div>
                        <div class="flex-1">
                            <p class="text-sm text-gray-600">Uptime</p>
                            <% uptime_percentage = @system_health[:checks]&.values&.count { |c| c[:status] == 'healthy' }.to_f / [@system_health[:checks]&.size.to_f, 1].max * 100 %>
                            <p class="text-xl font-bold text-gray-800"><%= number_with_precision(uptime_percentage, precision: 2) %>%</p>
                            <p class="text-xs text-gray-500 mt-1">Last 24 hours</p>
                        </div>
                    </div>
                </div>

                <!-- Active Pipelines -->
                <div class="glassmorphism rounded-xl p-6 metric-card animate-slide-in" style="animation-delay: 0.1s">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-sm font-semibold text-gray-600 uppercase tracking-wider">Active Pipelines</h3>
                        <i data-feather="activity" class="w-5 h-5 text-teal-600"></i>
                    </div>
                    <div class="space-y-3">
                        <%
                          running_count = @active_pipelines.where(status: 'running').count
                          pending_count = @active_pipelines.where(status: 'pending').count
                          failed_count = current_organization.pipeline_executions.where(status: 'failed', created_at: 24.hours.ago..Time.current).count
                          total_active = running_count + pending_count
                          running_percentage = total_active > 0 ? (running_count.to_f / total_active * 100).round : 0
                        %>
                        <div>
                            <div class="flex items-center justify-between mb-1">
                                <span class="text-sm text-gray-600">Running</span>
                                <span class="text-sm font-semibold text-gray-800"><%= running_count %></span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div class="bg-green-500 h-2 rounded-full" style="width: <%= running_percentage %>%"></div>
                            </div>
                        </div>
                        <div class="flex items-center justify-between text-sm">
                            <div class="flex items-center space-x-2">
                                <span class="w-2 h-2 bg-yellow-500 rounded-full"></span>
                                <span class="text-gray-600">Queued: <%= pending_count %></span>
                            </div>
                            <div class="flex items-center space-x-2">
                                <span class="w-2 h-2 bg-red-500 rounded-full"></span>
                                <span class="text-gray-600">Failed: <%= failed_count %></span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Resource Usage -->
                <div class="glassmorphism rounded-xl p-6 metric-card animate-slide-in" style="animation-delay: 0.2s">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-sm font-semibold text-gray-600 uppercase tracking-wider">Resource Usage</h3>
                        <i data-feather="cpu" class="w-5 h-5 text-purple-600"></i>
                    </div>
                    <div class="space-y-3">
                        <div>
                            <div class="flex items-center justify-between text-sm mb-1">
                                <span class="text-gray-600">CPU</span>
                                <span class="font-semibold text-gray-800"><%= @current_metrics[:cpu_usage].round(1) %>%</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-1.5">
                                <div class="bg-gradient-to-r from-purple-500 to-purple-600 h-1.5 rounded-full" style="width: <%= @current_metrics[:cpu_usage] %>%"></div>
                            </div>
                        </div>
                        <div>
                            <div class="flex items-center justify-between text-sm mb-1">
                                <span class="text-gray-600">Memory</span>
                                <span class="font-semibold text-gray-800"><%= @current_metrics[:memory_usage].round(1) %>%</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-1.5">
                                <div class="bg-gradient-to-r from-orange-500 to-orange-600 h-1.5 rounded-full" style="width: <%= @current_metrics[:memory_usage] %>%"></div>
                            </div>
                        </div>
                        <div>
                            <div class="flex items-center justify-between text-sm mb-1">
                                <span class="text-gray-600">Storage</span>
                                <span class="font-semibold text-gray-800"><%= @current_metrics[:storage_usage].round(1) %>%</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-1.5">
                                <div class="bg-gradient-to-r from-red-500 to-red-600 h-1.5 rounded-full" style="width: <%= @current_metrics[:storage_usage] %>%"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Alerts -->
                <div class="glassmorphism rounded-xl p-6 metric-card animate-slide-in" style="animation-delay: 0.3s">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-sm font-semibold text-gray-600 uppercase tracking-wider">Active Alerts</h3>
                        <i data-feather="alert-circle" class="w-5 h-5 text-red-600"></i>
                    </div>
                    <div class="space-y-2">
                        <%
                          critical_count = @recent_alerts.where(severity: 'critical').count
                          high_count = @recent_alerts.where(severity: 'high').count
                          medium_count = @recent_alerts.where(severity: 'medium').count
                          low_count = @recent_alerts.where(severity: 'low').count
                        %>
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-2">
                                <span class="w-2 h-2 bg-red-500 rounded-full"></span>
                                <span class="text-sm text-gray-700">Critical</span>
                            </div>
                            <span class="text-lg font-bold text-red-600"><%= critical_count %></span>
                        </div>
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-2">
                                <span class="w-2 h-2 bg-yellow-500 rounded-full"></span>
                                <span class="text-sm text-gray-700">Warning</span>
                            </div>
                            <span class="text-lg font-bold text-yellow-600"><%= high_count + medium_count %></span>
                        </div>
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-2">
                                <span class="w-2 h-2 bg-blue-500 rounded-full"></span>
                                <span class="text-sm text-gray-700">Info</span>
                            </div>
                            <span class="text-lg font-bold text-blue-600"><%= low_count %></span>
                        </div>
                    </div>
                    <button class="mt-3 text-sm text-teal-600 font-medium hover:text-teal-700">View All →</button>
                </div>
            </div>

            <!-- Main Monitoring Sections -->
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
                <!-- Live Pipeline Activity -->
                <div class="lg:col-span-2 glassmorphism rounded-xl p-6">
                    <div class="flex items-center justify-between mb-6">
                        <div class="flex items-center space-x-3">
                            <h3 class="text-lg font-semibold text-gray-800">Live Pipeline Activity</h3>
                            <div class="flex items-center space-x-2 text-sm">
                                <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                                <span class="text-green-600">Live</span>
                            </div>
                        </div>
                        <div class="flex items-center space-x-2">
                            <label class="text-sm text-gray-600">Auto-refresh</label>
                            <div class="toggle-switch active" onclick="toggleSwitch(this)"></div>
                        </div>
                    </div>
                    
                    <div class="space-y-4 max-h-[400px] overflow-y-auto pr-2">
                        <% if @active_pipelines.any? %>
                            <% @active_pipelines.limit(5).each do |execution| %>
                                <%
                                  status_colors = {
                                    'running' => { bg: 'bg-green-100', text: 'text-green-700', border: 'border-gray-200' },
                                    'pending' => { bg: 'bg-yellow-100', text: 'text-yellow-700', border: 'border-gray-200' },
                                    'failed' => { bg: 'bg-red-100', text: 'text-red-700', border: 'border-red-200' }
                                  }
                                  colors = status_colors[execution.status] || status_colors['pending']

                                  # Calculate progress and metrics
                                  progress_percentage = execution.progress_percentage || 0
                                  duration = execution.started_at ? time_ago_in_words(execution.started_at) : 'N/A'
                                  records_processed = execution.records_processed || 0
                                  total_records = execution.total_records || 0

                                  # Get latest pipeline metric for this execution
                                  latest_metric = execution.pipeline_metrics.order(recorded_at: :desc).first
                                  cpu_usage = latest_metric&.cpu_usage || 0
                                  memory_usage = latest_metric&.memory_usage_gb || 0
                                  records_per_second = latest_metric&.records_per_second || 0
                                %>
                                <div class="border <%= colors[:border] %> rounded-lg p-4 hover:border-teal-400 transition-all <%= 'bg-red-50' if execution.status == 'failed' %>">
                                    <div class="flex items-start justify-between mb-3">
                                        <div>
                                            <div class="flex items-center space-x-3">
                                                <h4 class="font-semibold text-gray-800"><%= execution.data_source&.name || 'Unknown Pipeline' %></h4>
                                                <span class="px-2 py-1 <%= colors[:bg] %> <%= colors[:text] %> rounded text-xs font-medium">
                                                    <%= execution.status.humanize %>
                                                </span>
                                            </div>
                                            <p class="text-sm text-gray-600 mt-1">
                                                <%= execution.data_source&.source_type&.humanize || 'Unknown' %> →
                                                <%= execution.destination_type&.humanize || 'Data Warehouse' %>
                                            </p>
                                        </div>
                                        <div class="flex items-center space-x-2">
                                            <% if execution.status == 'running' %>
                                                <button class="p-1.5 rounded hover:bg-gray-100 transition-colors">
                                                    <i data-feather="pause" class="w-4 h-4 text-gray-600"></i>
                                                </button>
                                            <% elsif execution.status == 'failed' %>
                                                <button class="p-1.5 rounded hover:bg-red-100 transition-colors">
                                                    <i data-feather="refresh-cw" class="w-4 h-4 text-gray-600"></i>
                                                </button>
                                            <% end %>
                                            <button class="p-1.5 rounded hover:bg-gray-100 transition-colors">
                                                <i data-feather="file-text" class="w-4 h-4 text-gray-600"></i>
                                            </button>
                                        </div>
                                    </div>

                                    <% if execution.status == 'running' && progress_percentage > 0 %>
                                        <div class="space-y-3">
                                            <div>
                                                <div class="flex items-center justify-between text-sm mb-1">
                                                    <span class="text-gray-600">Progress</span>
                                                    <span class="font-medium text-gray-800">
                                                        <%= progress_percentage.round(1) %>% •
                                                        <%= number_with_delimiter(records_processed) %> /
                                                        <%= number_with_delimiter(total_records) %> records
                                                    </span>
                                                </div>
                                                <div class="w-full bg-gray-200 rounded-full h-2 overflow-hidden">
                                                    <div class="bg-gradient-to-r from-green-500 to-green-600 h-2 rounded-full progress-bar transition-all duration-500"
                                                         style="width: <%= progress_percentage %>%"></div>
                                                </div>
                                            </div>

                                            <div class="grid grid-cols-5 gap-3 text-sm">
                                                <div>
                                                    <p class="text-gray-600">Duration</p>
                                                    <p class="font-medium text-gray-800"><%= duration %></p>
                                                </div>
                                                <div>
                                                    <p class="text-gray-600">Speed</p>
                                                    <p class="font-medium text-gray-800"><%= number_with_delimiter(records_per_second) %>/s</p>
                                                </div>
                                                <div>
                                                    <p class="text-gray-600">ETA</p>
                                                    <p class="font-medium text-gray-800">
                                                        <% if records_per_second > 0 && total_records > records_processed %>
                                                            <% eta_seconds = (total_records - records_processed) / records_per_second %>
                                                            <%= distance_of_time_in_words(eta_seconds.seconds) %>
                                                        <% else %>
                                                            N/A
                                                        <% end %>
                                                    </p>
                                                </div>
                                                <div>
                                                    <p class="text-gray-600">CPU</p>
                                                    <p class="font-medium text-gray-800"><%= cpu_usage.round(1) %>%</p>
                                                </div>
                                                <div>
                                                    <p class="text-gray-600">Memory</p>
                                                    <p class="font-medium text-gray-800"><%= memory_usage.round(1) %>GB</p>
                                                </div>
                                            </div>
                                        </div>
                                    <% elsif execution.status == 'pending' %>
                                        <div class="flex items-center space-x-2 text-sm text-gray-600">
                                            <i data-feather="clock" class="w-4 h-4"></i>
                                            <span>Waiting to start...</span>
                                        </div>
                                    <% elsif execution.status == 'failed' %>
                                        <div class="bg-red-100 rounded p-3 text-sm">
                                            <p class="text-red-800 font-medium mb-1">Error: <%= execution.error_message || 'Unknown error' %></p>
                                            <p class="text-red-700 text-xs"><%= execution.error_details || 'Check logs for more details' %></p>
                                        </div>
                                        <div class="flex items-center justify-between mt-3 text-sm">
                                            <span class="text-gray-600">Failed at: <%= execution.completed_at&.strftime('%l:%M:%S %p') || 'Unknown' %></span>
                                            <button class="text-red-600 font-medium hover:text-red-700">View Full Logs →</button>
                                        </div>
                                    <% end %>
                                </div>
                            <% end %>
                        <% else %>
                            <div class="text-center py-8 text-gray-500">
                                <i data-feather="activity" class="w-12 h-12 mx-auto mb-4 text-gray-300"></i>
                                <p class="text-lg font-medium">No Active Pipelines</p>
                                <p class="text-sm">All pipelines are currently idle</p>
                            </div>
                        <% end %>
                    </div>
                </div>

                <!-- System Metrics & Alerts -->
                <div class="space-y-6">
                    <!-- Real-time Metrics -->
                    <div class="glassmorphism rounded-xl p-6">
                        <h3 class="text-lg font-semibold text-gray-800 mb-4">Real-time Metrics</h3>
                        <div style="position: relative; height: 200px;">
                            <canvas id="realtimeChart"></canvas>
                        </div>
                    </div>
                    
                    <!-- Recent Alerts -->
                    <div class="glassmorphism rounded-xl p-6">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="text-lg font-semibold text-gray-800">Recent Alerts</h3>
                            <button class="text-sm text-teal-600 font-medium hover:text-teal-700">View All</button>
                        </div>
                        
                        <div class="space-y-3 max-h-48 overflow-y-auto">
                            <% if @recent_alerts.any? %>
                                <% @recent_alerts.limit(5).each do |alert| %>
                                    <%
                                      alert_styles = {
                                        'critical' => { class: 'critical', icon: 'alert-triangle', color: 'text-red-600' },
                                        'high' => { class: 'warning', icon: 'alert-circle', color: 'text-yellow-600' },
                                        'medium' => { class: 'warning', icon: 'alert-circle', color: 'text-yellow-600' },
                                        'low' => { class: 'info', icon: 'info', color: 'text-blue-600' }
                                      }
                                      style = alert_styles[alert.severity] || alert_styles['low']
                                    %>
                                    <div class="alert-item <%= style[:class] %> rounded-lg p-3">
                                        <div class="flex items-start space-x-3">
                                            <i data-feather="<%= style[:icon] %>" class="w-5 h-5 <%= style[:color] %> mt-0.5"></i>
                                            <div class="flex-1">
                                                <p class="text-sm font-medium text-gray-800"><%= alert.title %></p>
                                                <p class="text-xs text-gray-600 mt-1"><%= truncate(alert.message, length: 80) %></p>
                                                <p class="text-xs text-gray-500 mt-2"><%= time_ago_in_words(alert.created_at) %> ago</p>
                                            </div>
                                        </div>
                                    </div>
                                <% end %>
                            <% else %>
                                <div class="text-center py-6 text-gray-500">
                                    <i data-feather="check-circle" class="w-8 h-8 mx-auto mb-2 text-green-400"></i>
                                    <p class="text-sm font-medium">No Active Alerts</p>
                                    <p class="text-xs">All systems are running normally</p>
                                </div>
                            <% end %>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Performance Charts -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
                <div class="glassmorphism rounded-xl p-6">
                    <div class="flex items-center justify-between mb-6">
                        <h3 class="text-lg font-semibold text-gray-800">Pipeline Performance</h3>
                        <select class="px-3 py-1.5 bg-white border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-teal-500">
                            <option>Today</option>
                            <option>This Week</option>
                            <option>This Month</option>
                        </select>
                    </div>
                    <div style="position: relative; height: 300px;">
                        <canvas id="performanceChart"></canvas>
                    </div>
                </div>
                
                <div class="glassmorphism rounded-xl p-6">
                    <div class="flex items-center justify-between mb-6">
                        <h3 class="text-lg font-semibold text-gray-800">Resource Utilization</h3>
                        <select class="px-3 py-1.5 bg-white border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-teal-500">
                            <option>Last Hour</option>
                            <option>Last 6 Hours</option>
                            <option>Last 24 Hours</option>
                        </select>
                    </div>
                    <div style="position: relative; height: 300px;">
                        <canvas id="resourceChart"></canvas>
                    </div>
                </div>
            </div>

            <!-- Event Timeline & Logs -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Event Timeline -->
                <div class="glassmorphism rounded-xl p-6">
                    <h3 class="text-lg font-semibold text-gray-800 mb-6">Event Timeline</h3>
                    
                    <div class="space-y-4 max-h-96 overflow-y-auto pr-2">
                        <% if @timeline_events.any? %>
                            <% @timeline_events.limit(10).each do |event| %>
                                <%
                                  event_styles = {
                                    'pipeline_completed' => { dot: 'success', bg: 'bg-gray-50', color: 'bg-green-500' },
                                    'pipeline_failed' => { dot: 'error', bg: 'bg-red-50', color: 'bg-red-500' },
                                    'pipeline_started' => { dot: 'info', bg: 'bg-blue-50', color: 'bg-blue-500' },
                                    'alert_created' => { dot: 'warning', bg: 'bg-yellow-50', color: 'bg-yellow-500' },
                                    'system_health_check' => { dot: 'info', bg: 'bg-gray-50', color: 'bg-blue-500' },
                                    'error_occurred' => { dot: 'error', bg: 'bg-red-50', color: 'bg-red-500' }
                                  }
                                  style = event_styles[event.event_type] || event_styles['system_health_check']
                                %>
                                <div class="timeline-item">
                                    <div class="timeline-dot <%= style[:dot] %>">
                                        <div class="w-2 h-2 <%= style[:color] %> rounded-full"></div>
                                    </div>
                                    <div class="<%= style[:bg] %> rounded-lg p-4">
                                        <div class="flex items-start justify-between">
                                            <div>
                                                <p class="font-medium text-gray-800"><%= event.title %></p>
                                                <p class="text-sm text-gray-600 mt-1"><%= event.description %></p>
                                                <% if event.metadata.present? && event.metadata['details'] %>
                                                    <p class="text-xs text-gray-500 mt-2"><%= event.metadata['details'] %></p>
                                                <% end %>
                                            </div>
                                            <span class="text-xs text-gray-500 whitespace-nowrap">
                                                <%= event.occurred_at.strftime('%l:%M %p') %>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            <% end %>
                        <% else %>
                            <div class="text-center py-8 text-gray-500">
                                <i data-feather="clock" class="w-12 h-12 mx-auto mb-4 text-gray-300"></i>
                                <p class="text-lg font-medium">No Recent Events</p>
                                <p class="text-sm">System events will appear here</p>
                            </div>
                        <% end %>
                    </div>
                </div>

                <!-- System Logs -->
                <div class="glassmorphism rounded-xl p-6">
                    <div class="flex items-center justify-between mb-6">
                        <h3 class="text-lg font-semibold text-gray-800">System Logs</h3>
                        <div class="flex items-center space-x-2">
                            <select class="px-3 py-1.5 bg-gray-800 text-white border border-gray-700 rounded text-sm">
                                <option>All Levels</option>
                                <option>Errors Only</option>
                                <option>Warnings</option>
                                <option>Info</option>
                            </select>
                            <button class="p-1.5 bg-gray-800 text-white rounded hover:bg-gray-700">
                                <i data-feather="download" class="w-4 h-4"></i>
                            </button>
                        </div>
                    </div>
                    
                    <div class="log-viewer">
                        <%
                          # Combine timeline events and recent alerts to create log entries
                          log_entries = []

                          # Add timeline events as log entries
                          @timeline_events.limit(10).each do |event|
                            level = case event.event_type
                                   when 'pipeline_completed', 'data_sync_completed' then 'INFO'
                                   when 'pipeline_failed', 'data_sync_failed', 'error_occurred' then 'ERROR'
                                   when 'alert_created' then 'WARN'
                                   else 'INFO'
                                   end

                            log_entries << {
                              timestamp: event.occurred_at,
                              level: level,
                              message: "#{event.title}: #{event.description}"
                            }
                          end

                          # Add recent alerts as log entries
                          @recent_alerts.limit(5).each do |alert|
                            level = case alert.severity
                                   when 'critical' then 'ERROR'
                                   when 'high', 'medium' then 'WARN'
                                   else 'INFO'
                                   end

                            log_entries << {
                              timestamp: alert.created_at,
                              level: level,
                              message: "Alert: #{alert.title} - #{alert.message}"
                            }
                          end

                          # Sort by timestamp descending and take the most recent
                          log_entries = log_entries.sort_by { |entry| entry[:timestamp] }.reverse.first(15)
                        %>

                        <% if log_entries.any? %>
                            <% log_entries.each do |entry| %>
                                <div class="log-line">
                                    <span class="log-timestamp"><%= entry[:timestamp].strftime('%H:%M:%S') %></span>
                                    <span class="log-level <%= entry[:level].downcase %>"><%= entry[:level] %></span>
                                    <span class="log-message"><%= truncate(entry[:message], length: 100) %></span>
                                </div>
                            <% end %>
                        <% else %>
                            <div class="log-line">
                                <span class="log-timestamp"><%= Time.current.strftime('%H:%M:%S') %></span>
                                <span class="log-level info">INFO</span>
                                <span class="log-message">System monitoring active - no recent events</span>
                            </div>
                        <% end %>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script>
        // Pass data from Rails to JavaScript
        window.monitoringData = {
            currentMetrics: <%= raw @current_metrics.to_json %>,
            resourceMetrics: <%= raw @resource_metrics.limit(20).pluck(:recorded_at, :cpu_usage, :memory_usage, :storage_usage).to_json %>,
            pipelineStats: {
                running: <%= @active_pipelines.where(status: 'running').count %>,
                pending: <%= @active_pipelines.where(status: 'pending').count %>,
                failed: <%= current_organization.pipeline_executions.where(status: 'failed', created_at: 24.hours.ago..Time.current).count %>
            },
            pipelinePerformance: <%= raw @pipeline_performance.to_json %>
        };

        // Initialize Feather Icons
        feather.replace();

        // Toggle Switch
        function toggleSwitch(element) {
            element.classList.toggle('active');
        }

        // Real-time Metrics Chart
        function initRealtimeChart() {
            const ctx = document.getElementById('realtimeChart').getContext('2d');
            const currentMetrics = window.monitoringData.currentMetrics;

            const chart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: Array(20).fill(''),
                    datasets: [{
                        label: 'CPU',
                        data: Array(20).fill(currentMetrics.cpu_usage || 0),
                        borderColor: '#8B5CF6',
                        backgroundColor: 'transparent',
                        borderWidth: 2,
                        tension: 0.4,
                        pointRadius: 0
                    }, {
                        label: 'Memory',
                        data: Array(20).fill(currentMetrics.memory_usage || 0),
                        borderColor: '#F59E0B',
                        backgroundColor: 'transparent',
                        borderWidth: 2,
                        tension: 0.4,
                        pointRadius: 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    interaction: {
                        intersect: false,
                        mode: 'index'
                    },
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                usePointStyle: true,
                                padding: 15
                            }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100,
                            ticks: {
                                callback: function(value) {
                                    return value + '%';
                                }
                            },
                            grid: {
                                color: 'rgba(0, 0, 0, 0.05)'
                            }
                        },
                        x: {
                            display: false
                        }
                    }
                }
            });
        }

        // Pipeline Performance Chart
        function initPerformanceChart() {
            const ctx = document.getElementById('performanceChart').getContext('2d');
            const performanceData = window.monitoringData.pipelinePerformance;

            // Process the performance data
            const pipelineNames = [];
            const completedData = [];
            const failedData = [];
            const runningData = [];

            // Extract unique pipeline names
            const uniquePipelines = [...new Set(Object.keys(performanceData).map(key => key.split(',')[0]))];

            uniquePipelines.forEach(pipelineName => {
                pipelineNames.push(pipelineName);
                completedData.push(performanceData[`${pipelineName},completed`] || 0);
                failedData.push(performanceData[`${pipelineName},failed`] || 0);
                runningData.push(performanceData[`${pipelineName},running`] || 0);
            });

            // Fallback data if no performance data available
            if (pipelineNames.length === 0) {
                pipelineNames.push('No Data Available');
                completedData.push(0);
                failedData.push(0);
                runningData.push(0);
            }

            new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: pipelineNames,
                    datasets: [{
                        label: 'Completed',
                        data: completedData,
                        backgroundColor: '#10B981'
                    }, {
                        label: 'Failed',
                        data: failedData,
                        backgroundColor: '#EF4444'
                    }, {
                        label: 'Running',
                        data: runningData,
                        backgroundColor: '#3B82F6'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                usePointStyle: true,
                                padding: 15
                            }
                        }
                    },
                    scales: {
                        x: {
                            stacked: true,
                            grid: {
                                display: false
                            }
                        },
                        y: {
                            stacked: true,
                            grid: {
                                color: 'rgba(0, 0, 0, 0.05)'
                            }
                        }
                    }
                }
            });
        }

        // Resource Utilization Chart
        function initResourceChart() {
            const ctx = document.getElementById('resourceChart').getContext('2d');
            const resourceMetrics = window.monitoringData.resourceMetrics;

            // Process the resource metrics data
            let labels = [];
            let cpuData = [];
            let memoryData = [];
            let storageData = [];

            if (resourceMetrics && resourceMetrics.length > 0) {
                resourceMetrics.forEach(metric => {
                    const time = new Date(metric[0]);
                    labels.push(time.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit' }));
                    cpuData.push(metric[1] || 0);
                    memoryData.push(metric[2] || 0);
                    storageData.push(metric[3] || 0);
                });
            } else {
                // Fallback data if no metrics available
                labels = ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00', '24:00'];
                cpuData = [0, 0, 0, 0, 0, 0, 0];
                memoryData = [0, 0, 0, 0, 0, 0, 0];
                storageData = [0, 0, 0, 0, 0, 0, 0];
            }

            new Chart(ctx, {
                type: 'line',
                data: {
                    labels: labels,
                    datasets: [{
                        label: 'CPU Usage',
                        data: cpuData,
                        borderColor: '#8B5CF6',
                        backgroundColor: 'rgba(139, 92, 246, 0.1)',
                        tension: 0.4,
                        fill: true
                    }, {
                        label: 'Memory Usage',
                        data: memoryData,
                        borderColor: '#F59E0B',
                        backgroundColor: 'rgba(245, 158, 11, 0.1)',
                        tension: 0.4,
                        fill: true
                    }, {
                        label: 'Storage Usage',
                        data: storageData,
                        borderColor: '#EF4444',
                        backgroundColor: 'rgba(239, 68, 68, 0.1)',
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    interaction: {
                        intersect: false,
                        mode: 'index'
                    },
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                usePointStyle: true,
                                padding: 15
                            }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100,
                            ticks: {
                                callback: function(value) {
                                    return value + '%';
                                }
                            },
                            grid: {
                                color: 'rgba(0, 0, 0, 0.05)'
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            }
                        }
                    }
                }
            });
        }

        // Initialize all charts
        initRealtimeChart();
        initPerformanceChart();
        initResourceChart();

        // Simulate real-time updates
        function updateLiveData() {
            // Update progress bars
            const progressBars = document.querySelectorAll('.progress-bar');
            progressBars.forEach(bar => {
                const currentWidth = parseInt(bar.style.width || 0);
                if (currentWidth < 100) {
                    bar.style.width = Math.min(currentWidth + Math.random() * 5, 100) + '%';
                }
            });

            // Update metrics
            const metrics = document.querySelectorAll('[data-metric]');
            metrics.forEach(metric => {
                const current = parseFloat(metric.textContent);
                const change = (Math.random() - 0.5) * 2;
                metric.textContent = Math.max(0, Math.min(100, current + change)).toFixed(1) + '%';
            });
        }

        // Update every 3 seconds
        setInterval(updateLiveData, 3000);
    </script>
</body>
</html>