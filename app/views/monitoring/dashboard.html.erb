<% content_for :page_title, "System Monitoring" %>
<% content_for :page_subtitle, "Real-time monitoring of pipelines, system health, and performance metrics" %>

<div class="dashboard-content">
  <!-- Dashboard Header -->
  <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-8">
    <div class="mb-4 lg:mb-0">
      <h1 class="text-2xl font-bold mb-2" style="color: var(--color-text);">System Monitoring</h1>
      <p style="color: var(--color-text-secondary);">Real-time monitoring of pipelines, system health, and performance metrics</p>
    </div>
    <div class="flex flex-wrap gap-3 items-center">
      <!-- Time Range Selector -->
      <div data-controller="dropdown" class="relative">
        <button data-action="click->dropdown#toggle" class="btn btn--outline btn--sm inline-flex items-center gap-2">
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
          </svg>
          Last 24 Hours
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
          </svg>
        </button>
        <div data-dropdown-target="menu" class="hidden absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg z-50" style="background-color: var(--color-surface); border: 1px solid var(--color-card-border);">
          <a href="#" class="block px-4 py-2 text-sm hover:bg-gray-50" style="color: var(--color-text);">Last Hour</a>
          <a href="#" class="block px-4 py-2 text-sm hover:bg-gray-50" style="color: var(--color-text);">Last 24 Hours</a>
          <a href="#" class="block px-4 py-2 text-sm hover:bg-gray-50" style="color: var(--color-text);">Last 7 Days</a>
          <a href="#" class="block px-4 py-2 text-sm hover:bg-gray-50" style="color: var(--color-text);">Last 30 Days</a>
          <a href="#" class="block px-4 py-2 text-sm hover:bg-gray-50" style="color: var(--color-text);">Custom Range</a>
        </div>
      </div>

      <!-- Refresh Button -->
      <button data-controller="monitoring-refresh" data-action="click->monitoring-refresh#refresh" class="btn btn--outline btn--sm inline-flex items-center gap-2">
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
        </svg>
        Refresh
      </button>

      <!-- Export Report -->
      <%= link_to "#", class: "btn btn--primary btn--sm inline-flex items-center gap-2" do %>
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
        </svg>
        Export Report
      <% end %>
    </div>
  </div>

  <!-- Health Overview Metrics -->
  <div class="metrics-grid mb-8">
    <!-- System Health -->
    <div class="metric-card">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-sm font-semibold uppercase tracking-wider" style="color: var(--color-text-secondary);">System Health</h3>
        <div class="flex items-center gap-1">
          <% health_score = calculate_health_score(@system_health) %>
          <div class="w-2 h-2 rounded-full <%= health_status_class(health_score) %> animate-pulse"></div>
          <span class="text-xs font-medium <%= health_status_text_class(health_score) %>">
            <%= health_status_label(health_score) %>
          </span>
        </div>
      </div>
      <div class="flex items-center gap-4">
        <div class="relative">
          <%= render 'monitoring/health_ring', percentage: health_score %>
        </div>
        <div class="flex-1">
          <p class="text-sm" style="color: var(--color-text-secondary);">Uptime</p>
          <p class="text-xl font-bold" style="color: var(--color-text);">
            <%= calculate_uptime_percentage(@system_health) %>%
          </p>
          <p class="text-xs mt-1" style="color: var(--color-text-secondary);">Last 24 hours</p>
        </div>
      </div>
    </div>

    <!-- Active Pipelines -->
    <div class="metric-card">
      <div class="metric-icon">
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" style="color: var(--color-primary);">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
        </svg>
      </div>
      <div class="metric-content">
        <h3 style="font-size: var(--font-size-sm); color: var(--color-text-secondary); font-weight: 500; margin: 0 0 8px 0;">Active Pipelines</h3>
        <p class="metric-value" style="font-size: var(--font-size-3xl); font-weight: 600; color: var(--color-text); margin: 0 0 4px 0;">
          <%= @active_pipelines.count %>
        </p>
        <div class="flex items-center gap-4 text-sm">
          <span style="color: var(--color-success);">
            <span class="w-2 h-2 bg-green-500 rounded-full inline-block mr-1"></span>
            Running: <%= @active_pipelines.where(status: 'running').count %>
          </span>
          <span style="color: var(--color-warning);">
            <span class="w-2 h-2 bg-yellow-500 rounded-full inline-block mr-1"></span>
            Queued: <%= @active_pipelines.where(status: 'pending').count %>
          </span>
        </div>
      </div>
    </div>

    <!-- Resource Usage -->
    <div class="metric-card">
      <div class="metric-icon">
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" style="color: var(--color-text-secondary);">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z"/>
        </svg>
      </div>
      <div class="metric-content">
        <h3 style="font-size: var(--font-size-sm); color: var(--color-text-secondary); font-weight: 500; margin: 0 0 8px 0;">Resource Usage</h3>
        <div class="space-y-2">
          <%= render 'monitoring/resource_bar', label: 'CPU', value: @current_metrics[:cpu_usage], color: 'purple' %>
          <%= render 'monitoring/resource_bar', label: 'Memory', value: @current_metrics[:memory_usage], color: 'orange' %>
          <%= render 'monitoring/resource_bar', label: 'Storage', value: @current_metrics[:storage_usage], color: 'red' %>
        </div>
      </div>
    </div>

    <!-- Active Alerts -->
    <div class="metric-card">
      <div class="metric-icon">
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" style="color: var(--color-error);">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"/>
        </svg>
      </div>
      <div class="metric-content">
        <h3 style="font-size: var(--font-size-sm); color: var(--color-text-secondary); font-weight: 500; margin: 0 0 8px 0;">Active Alerts</h3>
        <div class="space-y-1">
          <% alert_counts = count_alerts_by_severity(@recent_alerts) %>
          <% alert_counts.each do |severity, count| %>
            <div class="flex items-center justify-between">
              <div class="flex items-center gap-2">
                <span class="w-2 h-2 rounded-full <%= alert_severity_color(severity) %>"></span>
                <span class="text-sm" style="color: var(--color-text);">
                  <%= severity.humanize %>
                </span>
              </div>
              <span class="text-lg font-bold <%= alert_severity_text_color(severity) %>">
                <%= count %>
              </span>
            </div>
          <% end %>
        </div>
        <%= link_to "View All →", "#", class: "text-sm font-medium mt-3 inline-block", style: "color: var(--color-primary);" %>
      </div>
    </div>
  </div>

  <!-- Main Monitoring Sections -->
  <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
    <!-- Live Pipeline Activity -->
    <div class="lg:col-span-2">
      <div class="chart-container">
        <div class="chart-header">
          <div class="flex items-center gap-3">
            <h3 class="text-lg font-semibold" style="color: var(--color-text);">Live Pipeline Activity</h3>
            <div class="flex items-center gap-2 text-sm">
              <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
              <span style="color: var(--color-success);">Live</span>
            </div>
          </div>
          <div class="flex items-center gap-2">
            <label class="text-sm" style="color: var(--color-text-secondary);">Auto-refresh</label>
            <div data-controller="toggle-switch" data-toggle-switch-active-value="true" class="relative">
              <button data-action="click->toggle-switch#toggle" class="toggle-switch active">
                <span class="toggle-slider"></span>
              </button>
            </div>
          </div>
        </div>
        
        <div class="space-y-4 max-h-96 overflow-y-auto" data-controller="pipeline-monitor">
          <%= render partial: 'monitoring/pipeline_item', collection: @active_pipelines.limit(5), as: :pipeline %>
          
          <% if @active_pipelines.empty? %>
            <div class="text-center py-8">
              <svg class="w-12 h-12 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" style="color: var(--color-text-secondary);">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
              </svg>
              <p class="text-lg font-medium" style="color: var(--color-text);">No Active Pipelines</p>
              <p class="text-sm" style="color: var(--color-text-secondary);">All pipelines are currently idle</p>
            </div>
          <% end %>
        </div>
      </div>
    </div>

    <!-- System Metrics & Alerts -->
    <div class="space-y-6">
      <!-- Real-time Metrics -->
      <div class="chart-container">
        <h3 class="text-lg font-semibold mb-4" style="color: var(--color-text);">Real-time Metrics</h3>
        <div style="position: relative; height: 200px;">
          <canvas id="realtimeChart" data-controller="realtime-chart" 
                  data-realtime-chart-metrics-value="<%= @current_metrics.to_json %>"></canvas>
        </div>
      </div>
      
      <!-- Recent Alerts -->
      <div class="chart-container">
        <div class="chart-header">
          <h3 class="text-lg font-semibold" style="color: var(--color-text);">Recent Alerts</h3>
          <%= link_to "View All", "#", class: "text-sm font-medium", style: "color: var(--color-primary);" %>
        </div>
        
        <div class="space-y-3 max-h-48 overflow-y-auto">
          <%= render partial: 'monitoring/alert_item', collection: @recent_alerts.limit(5), as: :alert %>
          
          <% if @recent_alerts.empty? %>
            <div class="text-center py-6">
              <svg class="w-8 h-8 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" style="color: var(--color-success);">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
              </svg>
              <p class="text-sm font-medium" style="color: var(--color-text);">No Active Alerts</p>
              <p class="text-xs" style="color: var(--color-text-secondary);">All systems are running normally</p>
            </div>
          <% end %>
        </div>
      </div>
    </div>
  </div>

  <!-- Performance Charts -->
  <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
    <div class="chart-container">
      <div class="chart-header">
        <h3 class="text-lg font-semibold" style="color: var(--color-text);">Pipeline Performance</h3>
        <div data-controller="dropdown" class="relative">
          <button data-action="click->dropdown#toggle" class="btn btn--outline btn--sm">
            Today
            <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
            </svg>
          </button>
          <div data-dropdown-target="menu" class="hidden absolute right-0 mt-2 w-32 bg-white rounded-lg shadow-lg z-50">
            <a href="#" class="block px-4 py-2 text-sm hover:bg-gray-50">Today</a>
            <a href="#" class="block px-4 py-2 text-sm hover:bg-gray-50">This Week</a>
            <a href="#" class="block px-4 py-2 text-sm hover:bg-gray-50">This Month</a>
          </div>
        </div>
      </div>
      <div style="position: relative; height: 300px;">
        <canvas id="performanceChart" data-controller="performance-chart"
                data-performance-chart-data-value="<%= @pipeline_performance.to_json %>"></canvas>
      </div>
    </div>
    
    <div class="chart-container">
      <div class="chart-header">
        <h3 class="text-lg font-semibold" style="color: var(--color-text);">Resource Utilization</h3>
        <div data-controller="dropdown" class="relative">
          <button data-action="click->dropdown#toggle" class="btn btn--outline btn--sm">
            Last Hour
            <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
            </svg>
          </button>
          <div data-dropdown-target="menu" class="hidden absolute right-0 mt-2 w-40 bg-white rounded-lg shadow-lg z-50">
            <a href="#" class="block px-4 py-2 text-sm hover:bg-gray-50">Last Hour</a>
            <a href="#" class="block px-4 py-2 text-sm hover:bg-gray-50">Last 6 Hours</a>
            <a href="#" class="block px-4 py-2 text-sm hover:bg-gray-50">Last 24 Hours</a>
          </div>
        </div>
      </div>
      <div style="position: relative; height: 300px;">
        <canvas id="resourceChart" data-controller="resource-chart"
                data-resource-chart-metrics-value="<%= resource_metrics_for_chart(@resource_metrics).to_json %>"></canvas>
      </div>
    </div>
  </div>

  <!-- Event Timeline & Logs -->
  <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
    <!-- Event Timeline -->
    <div class="chart-container">
      <h3 class="text-lg font-semibold mb-6" style="color: var(--color-text);">Event Timeline</h3>
      
      <div class="space-y-4 max-h-96 overflow-y-auto" data-controller="event-timeline">
        <%= render partial: 'monitoring/timeline_event', collection: @timeline_events.limit(10), as: :event %>
        
        <% if @timeline_events.empty? %>
          <div class="text-center py-8">
            <svg class="w-12 h-12 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" style="color: var(--color-text-secondary);">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
            </svg>
            <p class="text-lg font-medium" style="color: var(--color-text);">No Recent Events</p>
            <p class="text-sm" style="color: var(--color-text-secondary);">System events will appear here</p>
          </div>
        <% end %>
      </div>
    </div>

    <!-- System Logs -->
    <div class="chart-container">
      <div class="chart-header">
        <h3 class="text-lg font-semibold" style="color: var(--color-text);">System Logs</h3>
        <div class="flex items-center gap-2">
          <div data-controller="dropdown" class="relative">
            <button data-action="click->dropdown#toggle" class="btn btn--outline btn--sm">
              All Levels
              <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
              </svg>
            </button>
            <div data-dropdown-target="menu" class="hidden absolute right-0 mt-2 w-32 bg-white rounded-lg shadow-lg z-50">
              <a href="#" class="block px-4 py-2 text-sm hover:bg-gray-50">All Levels</a>
              <a href="#" class="block px-4 py-2 text-sm hover:bg-gray-50">Errors Only</a>
              <a href="#" class="block px-4 py-2 text-sm hover:bg-gray-50">Warnings</a>
              <a href="#" class="block px-4 py-2 text-sm hover:bg-gray-50">Info</a>
            </div>
          </div>
          <button class="btn btn--outline btn--sm p-2">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
            </svg>
          </button>
        </div>
      </div>
      
      <div class="log-viewer" data-controller="log-viewer">
        <%= render partial: 'monitoring/log_entry', collection: generate_log_entries(@timeline_events, @recent_alerts), as: :log %>
      </div>
    </div>
  </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>