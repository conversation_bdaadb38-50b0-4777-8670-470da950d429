<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Data Refinery Platform - Monitoring</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/feather-icons/4.29.0/feather.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        :root {
            --bg-cream: #fdfdfc;
            --teal-primary: #14B8A6;
            --teal-dark: #0D9488;
            --teal-light: #5EEAD4;
            --teal-50: #F0FDFA;
            --gray-800: #1F2937;
            --gray-600: #4B5563;
            --gray-400: #9CA3AF;
            --success: #10B981;
            --warning: #F59E0B;
            --error: #EF4444;
            --info: #3B82F6;
        }
        
        body {
            background-color: var(--bg-cream);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        
        .glassmorphism {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.4);
            box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.08);
        }
        
        .glassmorphism-dark {
            background: rgba(31, 41, 55, 0.95);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            color: white;
        }
        
        .teal-gradient {
            background: linear-gradient(135deg, var(--teal-primary) 0%, var(--teal-dark) 100%);
        }
        
        /* Status Indicators */
        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            position: relative;
        }
        
        .status-dot::after {
            content: '';
            position: absolute;
            top: -4px;
            left: -4px;
            right: -4px;
            bottom: -4px;
            border-radius: 50%;
            background: inherit;
            opacity: 0.3;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0%, 100% { 
                opacity: 0.3;
                transform: scale(1);
            }
            50% { 
                opacity: 0.1;
                transform: scale(1.2);
            }
        }
        
        /* Health Score Ring */
        .health-ring {
            transform: rotate(-90deg);
        }
        
        .health-ring-circle {
            transition: stroke-dashoffset 0.5s ease;
        }
        
        /* Timeline */
        .timeline-item {
            position: relative;
            padding-left: 30px;
        }
        
        .timeline-item::before {
            content: '';
            position: absolute;
            left: 8px;
            top: 24px;
            bottom: -20px;
            width: 2px;
            background: #e5e7eb;
        }
        
        .timeline-item:last-child::before {
            display: none;
        }
        
        .timeline-dot {
            position: absolute;
            left: 0;
            top: 6px;
            width: 18px;
            height: 18px;
            border-radius: 50%;
            background: white;
            border: 2px solid #e5e7eb;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .timeline-dot.success {
            border-color: var(--success);
        }
        
        .timeline-dot.error {
            border-color: var(--error);
        }
        
        .timeline-dot.warning {
            border-color: var(--warning);
        }
        
        .timeline-dot.info {
            border-color: var(--info);
        }
        
        /* Log viewer */
        .log-viewer {
            font-family: 'Monaco', 'Consolas', 'Courier New', monospace;
            font-size: 12px;
            line-height: 1.6;
            background: #1e293b;
            color: #e2e8f0;
            padding: 16px;
            border-radius: 8px;
            overflow: auto;
            max-height: 400px;
        }
        
        .log-line {
            display: flex;
            align-items: flex-start;
            margin-bottom: 4px;
        }
        
        .log-timestamp {
            color: #64748b;
            margin-right: 12px;
            flex-shrink: 0;
        }
        
        .log-level {
            font-weight: bold;
            margin-right: 8px;
            flex-shrink: 0;
            width: 50px;
        }
        
        .log-level.info { color: #3b82f6; }
        .log-level.success { color: #10b981; }
        .log-level.warning { color: #f59e0b; }
        .log-level.error { color: #ef4444; }
        
        .log-message {
            flex: 1;
        }
        
        /* Metric Cards */
        .metric-card {
            position: relative;
            overflow: hidden;
        }
        
        .metric-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            transition: left 0.5s;
        }
        
        .metric-card:hover::before {
            left: 100%;
        }
        
        /* Animations */
        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        
        @keyframes rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
        
        .animate-slide-in {
            animation: slideIn 0.5s ease-out;
        }
        
        .animate-fade-in {
            animation: fadeIn 0.3s ease-out;
        }
        
        .rotate-animation {
            animation: rotate 2s linear infinite;
        }
        
        /* Progress bars */
        .progress-bar {
            position: relative;
            overflow: hidden;
        }
        
        .progress-bar::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            bottom: 0;
            width: 40%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
            animation: shimmer 1.5s infinite;
        }
        
        @keyframes shimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(250%); }
        }
        
        /* Alert styles */
        .alert-item {
            border-left: 4px solid;
            transition: all 0.3s ease;
        }
        
        .alert-item:hover {
            transform: translateX(4px);
        }
        
        .alert-item.critical {
            border-left-color: var(--error);
            background: rgba(239, 68, 68, 0.05);
        }
        
        .alert-item.warning {
            border-left-color: var(--warning);
            background: rgba(245, 158, 11, 0.05);
        }
        
        .alert-item.info {
            border-left-color: var(--info);
            background: rgba(59, 130, 246, 0.05);
        }
        
        /* Toggle Switch */
        .toggle-switch {
            position: relative;
            width: 44px;
            height: 24px;
            background: var(--gray-400);
            border-radius: 12px;
            cursor: pointer;
            transition: background 0.3s;
        }
        
        .toggle-switch.active {
            background: var(--teal-primary);
        }
        
        .toggle-switch::after {
            content: '';
            position: absolute;
            top: 2px;
            left: 2px;
            width: 20px;
            height: 20px;
            background: white;
            border-radius: 50%;
            transition: transform 0.3s;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }
        
        .toggle-switch.active::after {
            transform: translateX(20px);
        }
        
        /* Custom Scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }
        
        ::-webkit-scrollbar-track {
            background: rgba(0, 0, 0, 0.05);
            border-radius: 4px;
        }
        
        ::-webkit-scrollbar-thumb {
            background: var(--teal-primary);
            border-radius: 4px;
        }
        
        ::-webkit-scrollbar-thumb:hover {
            background: var(--teal-dark);
        }
    </style>
</head>
<body class="min-h-screen">
    <!-- Main Content -->
    <main class="pt-20 pb-8">
        <div class="container mx-auto px-6 py-8">
            <!-- Page Header -->
            <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-8">
                <div class="mb-4 lg:mb-0">
                    <h2 class="text-3xl font-bold text-gray-800 mb-2">System Monitoring</h2>
                    <p class="text-gray-600">Real-time monitoring of pipelines, system health, and performance metrics</p>
                </div>
                <div class="flex flex-wrap gap-3">
                    <select class="px-4 py-2 bg-white border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-teal-500 text-sm">
                        <option>Last 24 Hours</option>
                        <option>Last 7 Days</option>
                        <option>Last 30 Days</option>
                        <option>Custom Range</option>
                    </select>
                    <button class="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg font-medium hover:bg-gray-200 transition-colors flex items-center space-x-2">
                        <i data-feather="refresh-cw" class="w-4 h-4"></i>
                        <span>Refresh</span>
                    </button>
                    <button class="px-4 py-2 teal-gradient text-white rounded-lg font-medium hover:opacity-90 transition-opacity flex items-center space-x-2">
                        <i data-feather="download" class="w-4 h-4"></i>
                        <span>Export Report</span>
                    </button>
                </div>
            </div>

            <!-- Health Overview -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <!-- System Health -->
                <div class="glassmorphism rounded-xl p-6 metric-card animate-slide-in">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-sm font-semibold text-gray-600 uppercase tracking-wider">System Health</h3>
                        <div class="flex items-center space-x-1">
                            <% health_percentage = health_percentage(@system_health) %>
                            <% health_status = health_percentage >= 80 ? 'healthy' : (health_percentage >= 60 ? 'warning' : 'critical') %>
                            <div class="status-dot <%= health_status == 'healthy' ? 'bg-green-500' : (health_status == 'warning' ? 'bg-yellow-500' : 'bg-red-500') %>"></div>
                            <span class="text-xs <%= health_status == 'healthy' ? 'text-green-600' : (health_status == 'warning' ? 'text-yellow-600' : 'text-red-600') %> font-medium">
                                <%= health_status.humanize %>
                            </span>
                        </div>
                    </div>
                    <div class="flex items-center space-x-4">
                        <div class="relative w-20 h-20">
                            <svg class="health-ring w-20 h-20">
                                <circle cx="40" cy="40" r="36" stroke="#e5e7eb" stroke-width="8" fill="none"></circle>
                                <% stroke_color = health_status == 'healthy' ? '#10B981' : (health_status == 'warning' ? '#F59E0B' : '#EF4444') %>
                                <% stroke_offset = 226 - (226 * health_percentage / 100) %>
                                <circle class="health-ring-circle" cx="40" cy="40" r="36"
                                        stroke="<%= stroke_color %>" stroke-width="8" fill="none"
                                        stroke-dasharray="226" stroke-dashoffset="<%= stroke_offset %>" stroke-linecap="round"></circle>
                            </svg>
                            <div class="absolute inset-0 flex items-center justify-center">
                                <span class="text-2xl font-bold text-gray-800"><%= health_percentage %>%</span>
                            </div>
                        </div>
                        <div class="flex-1">
                            <p class="text-sm text-gray-600">Uptime</p>
                            <% uptime_percentage = @system_health[:checks]&.values&.count { |c| c[:status] == 'healthy' }.to_f / [@system_health[:checks]&.size.to_f, 1].max * 100 %>
                            <p class="text-xl font-bold text-gray-800"><%= number_with_precision(uptime_percentage, precision: 2) %>%</p>
                            <p class="text-xs text-gray-500 mt-1">Last 24 hours</p>
                        </div>
                    </div>
                </div>

                <!-- Active Pipelines -->
                <div class="glassmorphism rounded-xl p-6 metric-card animate-slide-in" style="animation-delay: 0.1s">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-sm font-semibold text-gray-600 uppercase tracking-wider">Active Pipelines</h3>
                        <i data-feather="activity" class="w-5 h-5 text-teal-600"></i>
                    </div>
                    <div class="space-y-3">
                        <%
                          running_count = @active_pipelines.where(status: 'running').count
                          pending_count = @active_pipelines.where(status: 'pending').count
                          failed_count = current_organization.pipeline_executions.where(status: 'failed', created_at: 24.hours.ago..Time.current).count
                          total_active = running_count + pending_count
                          running_percentage = total_active > 0 ? (running_count.to_f / total_active * 100).round : 0
                        %>
                        <div>
                            <div class="flex items-center justify-between mb-1">
                                <span class="text-sm text-gray-600">Running</span>
                                <span class="text-sm font-semibold text-gray-800"><%= running_count %></span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div class="bg-green-500 h-2 rounded-full" style="width: <%= running_percentage %>%"></div>
                            </div>
                        </div>
                        <div class="flex items-center justify-between text-sm">
                            <div class="flex items-center space-x-2">
                                <span class="w-2 h-2 bg-yellow-500 rounded-full"></span>
                                <span class="text-gray-600">Queued: <%= pending_count %></span>
                            </div>
                            <div class="flex items-center space-x-2">
                                <span class="w-2 h-2 bg-red-500 rounded-full"></span>
                                <span class="text-gray-600">Failed: <%= failed_count %></span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Resource Usage -->
                <div class="glassmorphism rounded-xl p-6 metric-card animate-slide-in" style="animation-delay: 0.2s">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-sm font-semibold text-gray-600 uppercase tracking-wider">Resource Usage</h3>
                        <i data-feather="cpu" class="w-5 h-5 text-purple-600"></i>
                    </div>
                    <div class="space-y-3">
                        <div>
                            <div class="flex items-center justify-between text-sm mb-1">
                                <span class="text-gray-600">CPU</span>
                                <span class="font-semibold text-gray-800">42%</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-1.5">
                                <div class="bg-gradient-to-r from-purple-500 to-purple-600 h-1.5 rounded-full" style="width: 42%"></div>
                            </div>
                        </div>
                        <div>
                            <div class="flex items-center justify-between text-sm mb-1">
                                <span class="text-gray-600">Memory</span>
                                <span class="font-semibold text-gray-800">68%</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-1.5">
                                <div class="bg-gradient-to-r from-orange-500 to-orange-600 h-1.5 rounded-full" style="width: 68%"></div>
                            </div>
                        </div>
                        <div>
                            <div class="flex items-center justify-between text-sm mb-1">
                                <span class="text-gray-600">Storage</span>
                                <span class="font-semibold text-gray-800">81%</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-1.5">
                                <div class="bg-gradient-to-r from-red-500 to-red-600 h-1.5 rounded-full" style="width: 81%"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Alerts -->
                <div class="glassmorphism rounded-xl p-6 metric-card animate-slide-in" style="animation-delay: 0.3s">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-sm font-semibold text-gray-600 uppercase tracking-wider">Active Alerts</h3>
                        <i data-feather="alert-circle" class="w-5 h-5 text-red-600"></i>
                    </div>
                    <div class="space-y-2">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-2">
                                <span class="w-2 h-2 bg-red-500 rounded-full"></span>
                                <span class="text-sm text-gray-700">Critical</span>
                            </div>
                            <span class="text-lg font-bold text-red-600">2</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-2">
                                <span class="w-2 h-2 bg-yellow-500 rounded-full"></span>
                                <span class="text-sm text-gray-700">Warning</span>
                            </div>
                            <span class="text-lg font-bold text-yellow-600">5</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-2">
                                <span class="w-2 h-2 bg-blue-500 rounded-full"></span>
                                <span class="text-sm text-gray-700">Info</span>
                            </div>
                            <span class="text-lg font-bold text-blue-600">12</span>
                        </div>
                    </div>
                    <button class="mt-3 text-sm text-teal-600 font-medium hover:text-teal-700">View All →</button>
                </div>
            </div>

            <!-- Main Monitoring Sections -->
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
                <!-- Live Pipeline Activity -->
                <div class="lg:col-span-2 glassmorphism rounded-xl p-6">
                    <div class="flex items-center justify-between mb-6">
                        <div class="flex items-center space-x-3">
                            <h3 class="text-lg font-semibold text-gray-800">Live Pipeline Activity</h3>
                            <div class="flex items-center space-x-2 text-sm">
                                <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                                <span class="text-green-600">Live</span>
                            </div>
                        </div>
                        <div class="flex items-center space-x-2">
                            <label class="text-sm text-gray-600">Auto-refresh</label>
                            <div class="toggle-switch active" onclick="toggleSwitch(this)"></div>
                        </div>
                    </div>
                    
                    <div class="space-y-4 max-h-[400px] overflow-y-auto pr-2">
                        <!-- Running Pipeline 1 -->
                        <div class="border border-gray-200 rounded-lg p-4 hover:border-teal-400 transition-all">
                            <div class="flex items-start justify-between mb-3">
                                <div>
                                    <div class="flex items-center space-x-3">
                                        <h4 class="font-semibold text-gray-800">Sales Data ETL</h4>
                                        <span class="px-2 py-1 bg-green-100 text-green-700 rounded text-xs font-medium">Running</span>
                                    </div>
                                    <p class="text-sm text-gray-600 mt-1">Shopify → BigQuery</p>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <button class="p-1.5 rounded hover:bg-gray-100 transition-colors">
                                        <i data-feather="pause" class="w-4 h-4 text-gray-600"></i>
                                    </button>
                                    <button class="p-1.5 rounded hover:bg-gray-100 transition-colors">
                                        <i data-feather="x" class="w-4 h-4 text-gray-600"></i>
                                    </button>
                                </div>
                            </div>
                            
                            <div class="space-y-3">
                                <div>
                                    <div class="flex items-center justify-between text-sm mb-1">
                                        <span class="text-gray-600">Progress</span>
                                        <span class="font-medium text-gray-800">67% • 30,245 / 45,000 records</span>
                                    </div>
                                    <div class="w-full bg-gray-200 rounded-full h-2 overflow-hidden">
                                        <div class="bg-gradient-to-r from-green-500 to-green-600 h-2 rounded-full progress-bar transition-all duration-500" style="width: 67%"></div>
                                    </div>
                                </div>
                                
                                <div class="grid grid-cols-5 gap-3 text-sm">
                                    <div>
                                        <p class="text-gray-600">Duration</p>
                                        <p class="font-medium text-gray-800">4m 23s</p>
                                    </div>
                                    <div>
                                        <p class="text-gray-600">Speed</p>
                                        <p class="font-medium text-gray-800">1,250/s</p>
                                    </div>
                                    <div>
                                        <p class="text-gray-600">ETA</p>
                                        <p class="font-medium text-gray-800">2m 10s</p>
                                    </div>
                                    <div>
                                        <p class="text-gray-600">CPU</p>
                                        <p class="font-medium text-gray-800">35%</p>
                                    </div>
                                    <div>
                                        <p class="text-gray-600">Memory</p>
                                        <p class="font-medium text-gray-800">2.1GB</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Running Pipeline 2 -->
                        <div class="border border-gray-200 rounded-lg p-4 hover:border-teal-400 transition-all">
                            <div class="flex items-start justify-between mb-3">
                                <div>
                                    <div class="flex items-center space-x-3">
                                        <h4 class="font-semibold text-gray-800">Customer Analytics</h4>
                                        <span class="px-2 py-1 bg-blue-100 text-blue-700 rounded text-xs font-medium">Initializing</span>
                                    </div>
                                    <p class="text-sm text-gray-600 mt-1">Google Analytics → Snowflake</p>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <button class="p-1.5 rounded hover:bg-gray-100 transition-colors">
                                        <i data-feather="play" class="w-4 h-4 text-gray-600"></i>
                                    </button>
                                    <button class="p-1.5 rounded hover:bg-gray-100 transition-colors">
                                        <i data-feather="x" class="w-4 h-4 text-gray-600"></i>
                                    </button>
                                </div>
                            </div>
                            
                            <div class="flex items-center space-x-2 text-sm text-gray-600">
                                <i data-feather="loader" class="w-4 h-4 rotate-animation"></i>
                                <span>Connecting to source database...</span>
                            </div>
                        </div>

                        <!-- Failed Pipeline -->
                        <div class="border border-red-200 rounded-lg p-4 bg-red-50">
                            <div class="flex items-start justify-between mb-3">
                                <div>
                                    <div class="flex items-center space-x-3">
                                        <h4 class="font-semibold text-gray-800">Inventory Sync</h4>
                                        <span class="px-2 py-1 bg-red-100 text-red-700 rounded text-xs font-medium">Failed</span>
                                    </div>
                                    <p class="text-sm text-gray-600 mt-1">WooCommerce → PostgreSQL</p>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <button class="p-1.5 rounded hover:bg-red-100 transition-colors">
                                        <i data-feather="refresh-cw" class="w-4 h-4 text-gray-600"></i>
                                    </button>
                                    <button class="p-1.5 rounded hover:bg-red-100 transition-colors">
                                        <i data-feather="file-text" class="w-4 h-4 text-gray-600"></i>
                                    </button>
                                </div>
                            </div>
                            
                            <div class="bg-red-100 rounded p-3 text-sm">
                                <p class="text-red-800 font-medium mb-1">Error: Connection timeout</p>
                                <p class="text-red-700 text-xs">Failed to connect to source database after 3 attempts. Check network connectivity and credentials.</p>
                            </div>
                            
                            <div class="flex items-center justify-between mt-3 text-sm">
                                <span class="text-gray-600">Failed at: 12:34:56 PM</span>
                                <button class="text-red-600 font-medium hover:text-red-700">View Full Logs →</button>
                            </div>
                        </div>

                        <!-- Queued Pipeline -->
                        <div class="border border-gray-200 rounded-lg p-4 hover:border-teal-400 transition-all opacity-75">
                            <div class="flex items-start justify-between mb-3">
                                <div>
                                    <div class="flex items-center space-x-3">
                                        <h4 class="font-semibold text-gray-800">Marketing Data Sync</h4>
                                        <span class="px-2 py-1 bg-yellow-100 text-yellow-700 rounded text-xs font-medium">Queued</span>
                                    </div>
                                    <p class="text-sm text-gray-600 mt-1">Mailchimp → Data Lake</p>
                                </div>
                            </div>
                            
                            <div class="flex items-center justify-between text-sm text-gray-600">
                                <span>Position in queue: #3</span>
                                <span>Est. start: 5 minutes</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- System Metrics & Alerts -->
                <div class="space-y-6">
                    <!-- Real-time Metrics -->
                    <div class="glassmorphism rounded-xl p-6">
                        <h3 class="text-lg font-semibold text-gray-800 mb-4">Real-time Metrics</h3>
                        <div style="position: relative; height: 200px;">
                            <canvas id="realtimeChart"></canvas>
                        </div>
                    </div>
                    
                    <!-- Recent Alerts -->
                    <div class="glassmorphism rounded-xl p-6">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="text-lg font-semibold text-gray-800">Recent Alerts</h3>
                            <button class="text-sm text-teal-600 font-medium hover:text-teal-700">View All</button>
                        </div>
                        
                        <div class="space-y-3 max-h-48 overflow-y-auto">
                            <div class="alert-item critical rounded-lg p-3">
                                <div class="flex items-start space-x-3">
                                    <i data-feather="alert-triangle" class="w-5 h-5 text-red-600 mt-0.5"></i>
                                    <div class="flex-1">
                                        <p class="text-sm font-medium text-gray-800">Storage Space Critical</p>
                                        <p class="text-xs text-gray-600 mt-1">Only 19% storage remaining. Consider archiving old data.</p>
                                        <p class="text-xs text-gray-500 mt-2">2 minutes ago</p>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="alert-item warning rounded-lg p-3">
                                <div class="flex items-start space-x-3">
                                    <i data-feather="alert-circle" class="w-5 h-5 text-yellow-600 mt-0.5"></i>
                                    <div class="flex-1">
                                        <p class="text-sm font-medium text-gray-800">High Memory Usage</p>
                                        <p class="text-xs text-gray-600 mt-1">Pipeline "Sales ETL" using 85% of allocated memory.</p>
                                        <p class="text-xs text-gray-500 mt-2">15 minutes ago</p>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="alert-item info rounded-lg p-3">
                                <div class="flex items-start space-x-3">
                                    <i data-feather="info" class="w-5 h-5 text-blue-600 mt-0.5"></i>
                                    <div class="flex-1">
                                        <p class="text-sm font-medium text-gray-800">Scheduled Maintenance</p>
                                        <p class="text-xs text-gray-600 mt-1">System update scheduled for Sunday 2 AM EST.</p>
                                        <p class="text-xs text-gray-500 mt-2">1 hour ago</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Performance Charts -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
                <div class="glassmorphism rounded-xl p-6">
                    <div class="flex items-center justify-between mb-6">
                        <h3 class="text-lg font-semibold text-gray-800">Pipeline Performance</h3>
                        <select class="px-3 py-1.5 bg-white border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-teal-500">
                            <option>Today</option>
                            <option>This Week</option>
                            <option>This Month</option>
                        </select>
                    </div>
                    <div style="position: relative; height: 300px;">
                        <canvas id="performanceChart"></canvas>
                    </div>
                </div>
                
                <div class="glassmorphism rounded-xl p-6">
                    <div class="flex items-center justify-between mb-6">
                        <h3 class="text-lg font-semibold text-gray-800">Resource Utilization</h3>
                        <select class="px-3 py-1.5 bg-white border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-teal-500">
                            <option>Last Hour</option>
                            <option>Last 6 Hours</option>
                            <option>Last 24 Hours</option>
                        </select>
                    </div>
                    <div style="position: relative; height: 300px;">
                        <canvas id="resourceChart"></canvas>
                    </div>
                </div>
            </div>

            <!-- Event Timeline & Logs -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Event Timeline -->
                <div class="glassmorphism rounded-xl p-6">
                    <h3 class="text-lg font-semibold text-gray-800 mb-6">Event Timeline</h3>
                    
                    <div class="space-y-4 max-h-96 overflow-y-auto pr-2">
                        <div class="timeline-item">
                            <div class="timeline-dot success">
                                <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                            </div>
                            <div class="bg-gray-50 rounded-lg p-4">
                                <div class="flex items-start justify-between">
                                    <div>
                                        <p class="font-medium text-gray-800">Pipeline Completed</p>
                                        <p class="text-sm text-gray-600 mt-1">Sales Data ETL finished successfully</p>
                                        <p class="text-xs text-gray-500 mt-2">45,230 records processed in 12m 34s</p>
                                    </div>
                                    <span class="text-xs text-gray-500 whitespace-nowrap">2:45 PM</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="timeline-item">
                            <div class="timeline-dot error">
                                <div class="w-2 h-2 bg-red-500 rounded-full"></div>
                            </div>
                            <div class="bg-red-50 rounded-lg p-4">
                                <div class="flex items-start justify-between">
                                    <div>
                                        <p class="font-medium text-gray-800">Pipeline Failed</p>
                                        <p class="text-sm text-gray-600 mt-1">Inventory Sync encountered an error</p>
                                        <p class="text-xs text-red-600 mt-2">Connection timeout after 3 retries</p>
                                    </div>
                                    <span class="text-xs text-gray-500 whitespace-nowrap">2:30 PM</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="timeline-item">
                            <div class="timeline-dot warning">
                                <div class="w-2 h-2 bg-yellow-500 rounded-full"></div>
                            </div>
                            <div class="bg-yellow-50 rounded-lg p-4">
                                <div class="flex items-start justify-between">
                                    <div>
                                        <p class="font-medium text-gray-800">Resource Alert</p>
                                        <p class="text-sm text-gray-600 mt-1">High memory usage detected</p>
                                        <p class="text-xs text-gray-500 mt-2">Customer Analytics pipeline using 85% memory</p>
                                    </div>
                                    <span class="text-xs text-gray-500 whitespace-nowrap">2:15 PM</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="timeline-item">
                            <div class="timeline-dot info">
                                <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
                            </div>
                            <div class="bg-blue-50 rounded-lg p-4">
                                <div class="flex items-start justify-between">
                                    <div>
                                        <p class="font-medium text-gray-800">System Update</p>
                                        <p class="text-sm text-gray-600 mt-1">New pipeline version deployed</p>
                                        <p class="text-xs text-gray-500 mt-2">Version 2.4.1 with performance improvements</p>
                                    </div>
                                    <span class="text-xs text-gray-500 whitespace-nowrap">1:00 PM</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- System Logs -->
                <div class="glassmorphism rounded-xl p-6">
                    <div class="flex items-center justify-between mb-6">
                        <h3 class="text-lg font-semibold text-gray-800">System Logs</h3>
                        <div class="flex items-center space-x-2">
                            <select class="px-3 py-1.5 bg-gray-800 text-white border border-gray-700 rounded text-sm">
                                <option>All Levels</option>
                                <option>Errors Only</option>
                                <option>Warnings</option>
                                <option>Info</option>
                            </select>
                            <button class="p-1.5 bg-gray-800 text-white rounded hover:bg-gray-700">
                                <i data-feather="download" class="w-4 h-4"></i>
                            </button>
                        </div>
                    </div>
                    
                    <div class="log-viewer">
                        <div class="log-line">
                            <span class="log-timestamp">14:45:23</span>
                            <span class="log-level success">INFO</span>
                            <span class="log-message">Pipeline "Sales Data ETL" completed successfully</span>
                        </div>
                        <div class="log-line">
                            <span class="log-timestamp">14:45:22</span>
                            <span class="log-level success">INFO</span>
                            <span class="log-message">Writing 45,230 records to BigQuery table "analytics.orders"</span>
                        </div>
                        <div class="log-line">
                            <span class="log-timestamp">14:43:15</span>
                            <span class="log-level info">INFO</span>
                            <span class="log-message">Processing batch 450/450 (100%)</span>
                        </div>
                        <div class="log-line">
                            <span class="log-timestamp">14:30:45</span>
                            <span class="log-level error">ERROR</span>
                            <span class="log-message">Failed to connect to WooCommerce API: Connection timeout</span>
                        </div>
                        <div class="log-line">
                            <span class="log-timestamp">14:30:40</span>
                            <span class="log-level warning">WARN</span>
                            <span class="log-message">Retry attempt 3/3 for pipeline "Inventory Sync"</span>
                        </div>
                        <div class="log-line">
                            <span class="log-timestamp">14:15:32</span>
                            <span class="log-level warning">WARN</span>
                            <span class="log-message">Memory usage at 85% for process PID 12345</span>
                        </div>
                        <div class="log-line">
                            <span class="log-timestamp">14:00:00</span>
                            <span class="log-level info">INFO</span>
                            <span class="log-message">Starting scheduled pipeline "Customer Analytics"</span>
                        </div>
                        <div class="log-line">
                            <span class="log-timestamp">13:00:00</span>
                            <span class="log-level success">INFO</span>
                            <span class="log-message">System update completed: v2.4.1 deployed successfully</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script>
        // Initialize Feather Icons
        feather.replace();

        // Toggle Switch
        function toggleSwitch(element) {
            element.classList.toggle('active');
        }

        // Real-time Metrics Chart
        function initRealtimeChart() {
            const ctx = document.getElementById('realtimeChart').getContext('2d');
            const chart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: Array(20).fill(''),
                    datasets: [{
                        label: 'CPU',
                        data: Array(20).fill(0).map(() => Math.random() * 60 + 20),
                        borderColor: '#8B5CF6',
                        backgroundColor: 'transparent',
                        borderWidth: 2,
                        tension: 0.4,
                        pointRadius: 0
                    }, {
                        label: 'Memory',
                        data: Array(20).fill(0).map(() => Math.random() * 40 + 40),
                        borderColor: '#F59E0B',
                        backgroundColor: 'transparent',
                        borderWidth: 2,
                        tension: 0.4,
                        pointRadius: 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    interaction: {
                        intersect: false,
                        mode: 'index'
                    },
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                usePointStyle: true,
                                padding: 15
                            }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100,
                            ticks: {
                                callback: function(value) {
                                    return value + '%';
                                }
                            },
                            grid: {
                                color: 'rgba(0, 0, 0, 0.05)'
                            }
                        },
                        x: {
                            display: false
                        }
                    }
                }
            });

            // Update chart data every 2 seconds
            setInterval(() => {
                chart.data.datasets.forEach(dataset => {
                    dataset.data.shift();
                    if (dataset.label === 'CPU') {
                        dataset.data.push(Math.random() * 60 + 20);
                    } else {
                        dataset.data.push(Math.random() * 40 + 40);
                    }
                });
                chart.update('none');
            }, 2000);
        }

        // Pipeline Performance Chart
        function initPerformanceChart() {
            const ctx = document.getElementById('performanceChart').getContext('2d');
            new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: ['Sales ETL', 'Customer Analytics', 'Inventory Sync', 'Marketing Data', 'Financial Reports', 'Product Catalog'],
                    datasets: [{
                        label: 'Successful',
                        data: [45, 38, 42, 35, 40, 36],
                        backgroundColor: '#10B981'
                    }, {
                        label: 'Failed',
                        data: [2, 1, 5, 0, 1, 2],
                        backgroundColor: '#EF4444'
                    }, {
                        label: 'In Progress',
                        data: [3, 2, 0, 4, 1, 2],
                        backgroundColor: '#3B82F6'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                usePointStyle: true,
                                padding: 15
                            }
                        }
                    },
                    scales: {
                        x: {
                            stacked: true,
                            grid: {
                                display: false
                            }
                        },
                        y: {
                            stacked: true,
                            grid: {
                                color: 'rgba(0, 0, 0, 0.05)'
                            }
                        }
                    }
                }
            });
        }

        // Resource Utilization Chart
        function initResourceChart() {
            const ctx = document.getElementById('resourceChart').getContext('2d');
            new Chart(ctx, {
                type: 'line',
                data: {
                    labels: ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00', '24:00'],
                    datasets: [{
                        label: 'CPU Usage',
                        data: [30, 35, 45, 60, 55, 42, 38],
                        borderColor: '#8B5CF6',
                        backgroundColor: 'rgba(139, 92, 246, 0.1)',
                        tension: 0.4,
                        fill: true
                    }, {
                        label: 'Memory Usage',
                        data: [50, 55, 65, 75, 70, 68, 62],
                        borderColor: '#F59E0B',
                        backgroundColor: 'rgba(245, 158, 11, 0.1)',
                        tension: 0.4,
                        fill: true
                    }, {
                        label: 'Storage Usage',
                        data: [70, 72, 74, 76, 78, 80, 81],
                        borderColor: '#EF4444',
                        backgroundColor: 'rgba(239, 68, 68, 0.1)',
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    interaction: {
                        intersect: false,
                        mode: 'index'
                    },
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                usePointStyle: true,
                                padding: 15
                            }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100,
                            ticks: {
                                callback: function(value) {
                                    return value + '%';
                                }
                            },
                            grid: {
                                color: 'rgba(0, 0, 0, 0.05)'
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            }
                        }
                    }
                }
            });
        }

        // Initialize all charts
        initRealtimeChart();
        initPerformanceChart();
        initResourceChart();

        // Simulate real-time updates
        function updateLiveData() {
            // Update progress bars
            const progressBars = document.querySelectorAll('.progress-bar');
            progressBars.forEach(bar => {
                const currentWidth = parseInt(bar.style.width || 0);
                if (currentWidth < 100) {
                    bar.style.width = Math.min(currentWidth + Math.random() * 5, 100) + '%';
                }
            });

            // Update metrics
            const metrics = document.querySelectorAll('[data-metric]');
            metrics.forEach(metric => {
                const current = parseFloat(metric.textContent);
                const change = (Math.random() - 0.5) * 2;
                metric.textContent = Math.max(0, Math.min(100, current + change)).toFixed(1) + '%';
            });
        }

        // Update every 3 seconds
        setInterval(updateLiveData, 3000);
    </script>
</body>
</html>