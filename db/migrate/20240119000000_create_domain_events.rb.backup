# frozen_string_literal: true

class CreateDomainEvents < ActiveRecord::Migration[7.1]
  def change
    create_table :domain_events, id: :uuid do |t|
      t.string :event_id, null: false
      t.string :event_type, null: false
      t.references :aggregate, polymorphic: true, type: :uuid, null: false
      t.jsonb :data, default: {}
      t.jsonb :metadata, default: {}
      t.datetime :occurred_at, null: false
      
      t.timestamps
      
      t.index :event_id, unique: true
      t.index [:aggregate_id, :aggregate_type]
      t.index :event_type
      t.index :occurred_at
      t.index :created_at
    end
  end
end
