class AddScheduleFieldsToPipelineConfigurations < ActiveRecord::Migration[8.0]
  def change
    add_column :pipeline_configurations, :schedule_type, :string
    add_column :pipeline_configurations, :schedule_expression, :string
    add_column :pipeline_configurations, :schedule_timezone, :string, default: 'UTC'
    
    # Index for finding scheduled pipelines
    add_index :pipeline_configurations, :schedule_type
    
    reversible do |dir|
      dir.up do
        PipelineConfiguration.find_each do |pipeline|
          if pipeline.schedule_config.present?
            schedule_type = pipeline.schedule_config['type'] || 
                          (pipeline.schedule_config['cron_expression'].present? ? 'cron' : nil) ||
                          (pipeline.schedule_config['interval_minutes'].present? ? 'interval' : nil)
            
            schedule_expression = pipeline.schedule_config['expression'] || 
                                pipeline.schedule_config['cron_expression'] || 
                                pipeline.schedule_config['interval_minutes']&.to_s ||
                                pipeline.schedule_config['time']
            
            pipeline.update_columns(
              schedule_type: schedule_type,
              schedule_expression: schedule_expression,
              schedule_timezone: pipeline.schedule_config['timezone'] || 'UTC'
            )
          end
        end
      end
      
      dir.down do
        # Preserve data back to JSON if rolling back
        PipelineConfiguration.find_each do |pipeline|
          if pipeline.schedule_type.present?
            schedule_config = {
              'type' => pipeline.schedule_type,
              'expression' => pipeline.schedule_expression,
              'timezone' => pipeline.schedule_timezone
            }
            
            # Map back to legacy field names based on type
            case pipeline.schedule_type
            when 'cron'
              schedule_config['cron_expression'] = pipeline.schedule_expression
            when 'interval'
              schedule_config['interval_minutes'] = pipeline.schedule_expression.to_i
            when 'daily'
              schedule_config['time'] = pipeline.schedule_expression
            end
            
            pipeline.update_column(:schedule_config, schedule_config)
          end
        end
      end
    end
  end
end
